<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Story Builder</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;700&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Inter', sans-serif; }
        /* Add custom scrollbar styling for a better look */
        ::-webkit-scrollbar { width: 8px; }
        ::-webkit-scrollbar-track { background: #1e293b; }
        ::-webkit-scrollbar-thumb { background: #475569; border-radius: 4px; }
        ::-webkit-scrollbar-thumb:hover { background: #64748b; }

        /* Category tab styling */
        .category-tab {
            transition: all 0.2s ease;
        }
        .category-tab:hover {
            background-color: rgba(99, 102, 241, 0.3);
        }
        .category-tab.active {
            background-color: rgb(99, 102, 241);
            color: white;
        }
    </style>
</head>
<body class="bg-slate-900 text-slate-200">

    <div class="flex h-screen overflow-hidden">
        <!-- Left Column: Brick Library -->
        <div id="brick-library-container" class="w-96 bg-slate-800 p-4 flex flex-col">
            <div class="flex justify-between items-center mb-4">
                <h2 class="text-xl font-bold">My Bricks</h2>
                <div class="relative">
                    <button id="add-new-brick-btn" class="bg-indigo-600 hover:bg-indigo-500 text-white font-bold py-2 px-4 rounded-lg transition-colors">+ New Brick</button>
                    <div id="brick-type-dropdown" class="absolute right-0 mt-2 w-48 bg-slate-700 rounded-lg shadow-lg hidden z-10">
                        <button class="w-full text-left px-4 py-2 hover:bg-slate-600 rounded-t-lg" data-brick-type="Character">Character</button>
                        <button class="w-full text-left px-4 py-2 hover:bg-slate-600" data-brick-type="Setting">Setting</button>
                        <button class="w-full text-left px-4 py-2 hover:bg-slate-600" data-brick-type="Plot">Plot</button>
                        <button class="w-full text-left px-4 py-2 hover:bg-slate-600 rounded-b-lg" data-brick-type="Theme">Theme</button>
                    </div>
                </div>
            </div>

            <!-- Category Tabs -->
            <div class="flex mb-4 bg-slate-900 rounded-lg p-1">
                <button class="category-tab flex-1 py-2 px-3 rounded-md text-sm font-medium transition-colors active" data-category="all">All</button>
                <button class="category-tab flex-1 py-2 px-3 rounded-md text-sm font-medium transition-colors" data-category="Character">Characters</button>
                <button class="category-tab flex-1 py-2 px-3 rounded-md text-sm font-medium transition-colors" data-category="Setting">Settings</button>
                <button class="category-tab flex-1 py-2 px-3 rounded-md text-sm font-medium transition-colors" data-category="Plot">Plots</button>
                <button class="category-tab flex-1 py-2 px-3 rounded-md text-sm font-medium transition-colors" data-category="Theme">Themes</button>
            </div>

            <div id="brick-library-list" class="flex-1 overflow-y-auto space-y-3 pr-2">
                <!-- Brick cards will be dynamically inserted here -->
            </div>
        </div>

        <!-- Center Column: Prompt Assembler -->
        <div id="prompt-assembler-container" class="flex-1 p-6 flex flex-col border-l border-r border-slate-700">
            <h2 class="text-2xl font-bold mb-4">Prompt Assembler</h2>
            <div id="prompt-assembler-slots" class="flex-1 bg-slate-900/50 rounded-lg p-4 space-y-4">
                 <div id="character-slot" data-slot-type="Character" class="h-24 border-2 border-dashed border-slate-600 rounded-lg flex items-center justify-center text-slate-500">Drop Character Bricks Here (multiple allowed)</div>
                 <div id="setting-slot" data-slot-type="Setting" class="h-24 border-2 border-dashed border-slate-600 rounded-lg flex items-center justify-center text-slate-500">Drop Setting Bricks Here (multiple allowed)</div>
                 <div id="plot-slot" data-slot-type="Plot" class="h-24 border-2 border-dashed border-slate-600 rounded-lg flex items-center justify-center text-slate-500">Drop Plot Bricks Here (multiple allowed)</div>
                 <div id="theme-slot" data-slot-type="Theme" class="h-24 border-2 border-dashed border-slate-600 rounded-lg flex items-center justify-center text-slate-500">Drop Theme Bricks Here (multiple allowed)</div>
            </div>
            <div class="mt-4">
                <h3 class="text-lg font-semibold mb-2">Final Prompt Preview</h3>
                <textarea id="final-prompt-preview" readonly class="w-full h-32 p-2 bg-slate-800 border border-slate-600 rounded-lg resize-none"></textarea>
                <button id="generate-story-btn" class="w-full mt-2 bg-green-600 hover:bg-green-500 text-white font-bold py-3 px-4 rounded-lg transition-colors text-lg">Generate Story</button>
            </div>
        </div>

        <!-- Right Column: AI Output -->
        <div id="ai-output-container" class="flex-1 p-6 flex flex-col">
            <h2 class="text-2xl font-bold mb-4">Generated Story</h2>
            <div id="ai-output-content" class="flex-1 bg-slate-800 rounded-lg p-4 overflow-y-auto whitespace-pre-wrap">Your generated story will appear here...</div>
        </div>
    </div>

    <!-- Modal for adding/editing a brick -->
    <div id="modal-overlay" class="fixed inset-0 bg-black/70 flex items-center justify-center hidden z-50">
        <div id="brick-modal" class="bg-slate-800 rounded-lg p-8 shadow-xl w-full max-w-2xl max-h-[90vh] overflow-y-auto">
            <h2 id="modal-title" class="text-2xl font-bold mb-6">Create New Brick</h2>
            <form id="brick-form">
                <input type="hidden" id="brick-type-input" value="">
                <input type="hidden" id="edit-brick-id" value="">

                <div class="space-y-4">
                    <div>
                        <label for="brick-name-input" class="block mb-1 font-semibold">Brick Name</label>
                        <input type="text" id="brick-name-input" class="w-full p-2 bg-slate-700 border border-slate-600 rounded-lg" required>
                    </div>

                    <!-- Character Fields -->
                    <div id="character-fields" class="space-y-4 hidden">
                        <div>
                            <label for="character-archetype-input" class="block mb-1 font-semibold">Character's Archetype</label>
                            <input type="text" id="character-archetype-input" class="w-full p-2 bg-slate-700 border border-slate-600 rounded-lg" placeholder="e.g., The Rebel, The Mentor">
                        </div>
                        <div>
                            <label for="character-backstory-input" class="block mb-1 font-semibold">Character's Backstory</label>
                            <textarea id="character-backstory-input" class="w-full p-2 bg-slate-700 border border-slate-600 rounded-lg h-24"></textarea>
                        </div>
                        <div>
                            <label for="character-goal-input" class="block mb-1 font-semibold">Character's Goal</label>
                            <textarea id="character-goal-input" class="w-full p-2 bg-slate-700 border border-slate-600 rounded-lg h-24"></textarea>
                        </div>
                    </div>

                    <!-- Setting Fields -->
                    <div id="setting-fields" class="space-y-4 hidden">
                        <div>
                            <label for="setting-location-input" class="block mb-1 font-semibold">Location</label>
                            <input type="text" id="setting-location-input" class="w-full p-2 bg-slate-700 border border-slate-600 rounded-lg" placeholder="e.g., Ancient castle, Modern city">
                        </div>
                        <div>
                            <label for="setting-time-input" class="block mb-1 font-semibold">Time Period</label>
                            <input type="text" id="setting-time-input" class="w-full p-2 bg-slate-700 border border-slate-600 rounded-lg" placeholder="e.g., Medieval times, Present day">
                        </div>
                        <div>
                            <label for="setting-atmosphere-input" class="block mb-1 font-semibold">Atmosphere & Mood</label>
                            <textarea id="setting-atmosphere-input" class="w-full p-2 bg-slate-700 border border-slate-600 rounded-lg h-24" placeholder="Describe the mood, weather, lighting, sounds..."></textarea>
                        </div>
                    </div>

                    <!-- Plot Fields -->
                    <div id="plot-fields" class="space-y-4 hidden">
                        <div>
                            <label for="plot-conflict-input" class="block mb-1 font-semibold">Main Conflict</label>
                            <textarea id="plot-conflict-input" class="w-full p-2 bg-slate-700 border border-slate-600 rounded-lg h-24" placeholder="What is the central problem or challenge?"></textarea>
                        </div>
                        <div>
                            <label for="plot-inciting-input" class="block mb-1 font-semibold">Inciting Incident</label>
                            <textarea id="plot-inciting-input" class="w-full p-2 bg-slate-700 border border-slate-600 rounded-lg h-24" placeholder="What event kicks off the story?"></textarea>
                        </div>
                        <div>
                            <label for="plot-resolution-input" class="block mb-1 font-semibold">Resolution Direction</label>
                            <textarea id="plot-resolution-input" class="w-full p-2 bg-slate-700 border border-slate-600 rounded-lg h-24" placeholder="How should the conflict be resolved?"></textarea>
                        </div>
                    </div>

                    <!-- Theme Fields -->
                    <div id="theme-fields" class="space-y-4 hidden">
                        <div>
                            <label for="theme-message-input" class="block mb-1 font-semibold">Central Message</label>
                            <input type="text" id="theme-message-input" class="w-full p-2 bg-slate-700 border border-slate-600 rounded-lg" placeholder="e.g., Redemption, Love conquers all">
                        </div>
                        <div>
                            <label for="theme-tone-input" class="block mb-1 font-semibold">Tone</label>
                            <input type="text" id="theme-tone-input" class="w-full p-2 bg-slate-700 border border-slate-600 rounded-lg" placeholder="e.g., Hopeful, Dark, Humorous">
                        </div>
                        <div>
                            <label for="theme-symbolism-input" class="block mb-1 font-semibold">Symbolism & Motifs</label>
                            <textarea id="theme-symbolism-input" class="w-full p-2 bg-slate-700 border border-slate-600 rounded-lg h-24" placeholder="What symbols or recurring elements support the theme?"></textarea>
                        </div>
                    </div>
                </div>

                <div class="flex justify-end space-x-4 mt-8">
                    <button type="button" id="cancel-brick-btn" class="bg-slate-600 hover:bg-slate-500 text-white font-bold py-2 px-4 rounded-lg">Cancel</button>
                    <button type="submit" id="save-brick-btn" class="bg-indigo-600 hover:bg-indigo-500 text-white font-bold py-2 px-4 rounded-lg">Save Brick</button>
                </div>
            </form>
        </div>
    </div>

    <script>
        // Pass initial data from PHP to JavaScript
        const initialBricks = <?php echo json_encode($initialBricks); ?>;
    </script>
    <script src="assets/js/app.js" type="module"></script>
</body>
</html>
