<?php
// Include the dashboard header
require_once __DIR__ . '/../../../templates/header.php';
?>

<style>
        /* Add custom scrollbar styling for a better look */
        ::-webkit-scrollbar { width: 8px; }
        ::-webkit-scrollbar-track { background: #1e293b; }
        ::-webkit-scrollbar-thumb { background: #475569; border-radius: 4px; }
        ::-webkit-scrollbar-thumb:hover { background: #64748b; }

        /* Tab styling */
        .category-tab, .library-tab, .story-tool-tab {
            transition: all 0.2s ease;
        }
        .category-tab:hover, .library-tab:hover, .story-tool-tab:hover {
            background-color: rgba(99, 102, 241, 0.3);
        }
        .category-tab.active, .library-tab.active, .story-tool-tab.active {
            background-color: rgb(99, 102, 241);
            color: white;
        }

        /* Column scrolling - account for dashboard header */
        .column-container {
            height: calc(100vh - 8rem);
            overflow-y: auto;
        }

        /* Story card styling */
        .line-clamp-2 {
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        /* Story configuration styling */
        select, input[type="checkbox"] {
            transition: all 0.2s ease;
        }

        select:focus, input[type="checkbox"]:focus {
            outline: none;
            ring: 2px;
            ring-color: rgb(99, 102, 241);
        }

        /* Copy button animation */
        #copy-story-btn {
            transition: all 0.3s ease;
        }

        /* AI assistance button styling */
        button[id*="ai"], button[id*="generate"], button[id*="deepen"], button[id*="dialogue"], button[id*="suggest"], button[id*="brainstorm"] {
            transition: all 0.2s ease;
        }

        button[id*="ai"]:disabled, button[id*="generate"]:disabled, button[id*="deepen"]:disabled, button[id*="dialogue"]:disabled, button[id*="suggest"]:disabled, button[id*="brainstorm"]:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        /* AI results modal styling */
        #ai-results-content pre {
            background: rgba(15, 23, 42, 0.5);
            padding: 1rem;
            border-radius: 0.5rem;
            border: 1px solid rgba(71, 85, 105, 0.3);
        }
    </style>

<!-- AI Story Builder Content -->
<div class="min-h-screen bg-slate-900 text-slate-200">
    <div class="flex overflow-hidden" style="height: calc(100vh - 6rem);">
        <!-- Left Column: Library -->
        <div id="library-container" class="w-96 bg-slate-800 p-4 flex flex-col column-container">
            <!-- Main Library Tabs -->
            <div class="flex mb-4 bg-slate-900 rounded-lg p-1">
                <button class="library-tab flex-1 py-2 px-3 rounded-md text-sm font-medium transition-colors active" data-library="blocks">Blocks</button>
                <button class="library-tab flex-1 py-2 px-3 rounded-md text-sm font-medium transition-colors" data-library="stories">Stories</button>
            </div>

            <!-- Block Library View -->
            <div id="block-library-view" class="flex-1 flex flex-col">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-xl font-bold">My Blocks</h2>
                    <div class="flex space-x-2">
                        <button id="generate-demo-blocks-btn" class="bg-purple-600 hover:bg-purple-500 text-white font-medium py-2 px-3 rounded-lg transition-colors text-sm" title="Generate example blocks to get started">
                            🎲 Demo Blocks
                        </button>
                        <div class="relative">
                            <button id="add-new-brick-btn" class="bg-indigo-600 hover:bg-indigo-500 text-white font-bold py-2 px-4 rounded-lg transition-colors">+ New Block</button>
                            <div id="block-type-dropdown" class="absolute right-0 mt-2 w-48 bg-slate-700 rounded-lg shadow-lg hidden z-10">
                                <button class="w-full text-left px-4 py-2 hover:bg-slate-600 rounded-t-lg" data-block-type="Character">Character</button>
                                <button class="w-full text-left px-4 py-2 hover:bg-slate-600" data-block-type="Setting">Setting</button>
                                <button class="w-full text-left px-4 py-2 hover:bg-slate-600" data-block-type="Plot">Plot</button>
                                <button class="w-full text-left px-4 py-2 hover:bg-slate-600 rounded-b-lg" data-block-type="Theme">Theme</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Block Category Tabs -->
                <div class="flex mb-4 bg-slate-900 rounded-lg p-1 text-xs">
                    <button class="category-tab flex-1 py-2 px-2 rounded-md font-medium transition-colors active" data-category="all">All</button>
                    <button class="category-tab flex-1 py-2 px-2 rounded-md font-medium transition-colors" data-category="Character">Chars</button>
                    <button class="category-tab flex-1 py-2 px-2 rounded-md font-medium transition-colors" data-category="Setting">Sets</button>
                    <button class="category-tab flex-1 py-2 px-2 rounded-md font-medium transition-colors" data-category="Plot">Plots</button>
                    <button class="category-tab flex-1 py-2 px-2 rounded-md font-medium transition-colors" data-category="Theme">Themes</button>
                </div>

                <div id="block-library-list" class="flex-1 overflow-y-auto space-y-3 pr-2">
                    <!-- Block cards will be dynamically inserted here -->
                </div>
            </div>

            <!-- Story Library View -->
            <div id="story-library-view" class="flex-1 flex-col hidden">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-xl font-bold">My Stories</h2>
                    <button id="save-current-story-btn" class="bg-green-600 hover:bg-green-500 text-white font-bold py-2 px-4 rounded-lg transition-colors">Save Current</button>
                </div>

                <div id="story-library-list" class="flex-1 overflow-y-auto space-y-3 pr-2">
                    <!-- Story cards will be dynamically inserted here -->
                </div>
            </div>
        </div>

        <!-- Center Column: Prompt Assembler -->
        <div id="prompt-assembler-container" class="flex-1 p-6 flex flex-col border-l border-r border-slate-700 column-container">
            <h2 class="text-2xl font-bold mb-4">Prompt Assembler</h2>
            <div id="prompt-assembler-slots" class="flex-1 bg-slate-900/50 rounded-lg p-4 space-y-4">
                 <div id="character-slot" data-slot-type="Character" class="h-24 border-2 border-dashed border-slate-600 rounded-lg flex items-center justify-center text-slate-500">Drop Character Bricks Here (multiple allowed)</div>
                 <div id="setting-slot" data-slot-type="Setting" class="h-24 border-2 border-dashed border-slate-600 rounded-lg flex items-center justify-center text-slate-500">Drop Setting Bricks Here (multiple allowed)</div>
                 <div id="plot-slot" data-slot-type="Plot" class="h-24 border-2 border-dashed border-slate-600 rounded-lg flex items-center justify-center text-slate-500">Drop Plot Bricks Here (multiple allowed)</div>
                 <div id="theme-slot" data-slot-type="Theme" class="h-24 border-2 border-dashed border-slate-600 rounded-lg flex items-center justify-center text-slate-500">Drop Theme Bricks Here (multiple allowed)</div>
            </div>

            <!-- Story Tools Tabs -->
            <div class="mt-4 bg-slate-800/50 rounded-lg p-4">
                <div class="flex space-x-1 mb-4">
                    <button class="story-tool-tab flex-1 py-2 px-3 rounded-md font-medium transition-colors active" data-tool="config">Story Config</button>
                    <button class="story-tool-tab flex-1 py-2 px-3 rounded-md font-medium transition-colors" data-tool="helpers">AI Helpers</button>
                </div>

                <!-- Story Configuration -->
                <div id="story-config-view" class="story-tool-view">
                    <h3 class="text-lg font-semibold mb-3">Story Configuration</h3>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <!-- Story Length -->
                    <div>
                        <label for="story-length-select" class="block text-sm font-medium mb-1">Story Length</label>
                        <select id="story-length-select" class="w-full p-2 bg-slate-700 border border-slate-600 rounded-lg text-sm">
                            <option value="flash-fiction">Flash Fiction (100-1000 words)</option>
                            <option value="short-story" selected>Short Story (1000-7500 words)</option>
                            <option value="novelette">Novelette (7500-17500 words)</option>
                            <option value="novel-chapter">Novel Chapter (2000-5000 words)</option>
                        </select>
                    </div>

                    <!-- Target Audience -->
                    <div>
                        <label for="target-audience-select" class="block text-sm font-medium mb-1">Target Audience</label>
                        <select id="target-audience-select" class="w-full p-2 bg-slate-700 border border-slate-600 rounded-lg text-sm">
                            <option value="children">Children (Ages 5-12)</option>
                            <option value="young-adult">Young Adult (Ages 13-17)</option>
                            <option value="adult" selected>Adult (Ages 18+)</option>
                        </select>
                    </div>
                </div>

                <!-- Continuation Checkbox -->
                <div class="mb-4">
                    <label class="flex items-center space-x-2 cursor-pointer">
                        <input type="checkbox" id="continuation-checkbox" class="w-4 h-4 text-indigo-600 bg-slate-700 border-slate-600 rounded focus:ring-indigo-500">
                        <span class="text-sm font-medium">This is a continuation (e.g., next chapter)</span>
                    </label>
                    <p class="text-xs text-slate-400 mt-1 ml-6">Check this if you want the story to continue from where a previous story left off</p>
                </div>
                </div>

                <!-- AI Story Helpers -->
                <div id="story-helpers-view" class="story-tool-view hidden">
                    <h3 class="text-lg font-semibold mb-3">AI Story Helpers</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-3 mb-4">
                    <button id="suggest-story-btn" class="bg-amber-600 hover:bg-amber-500 text-white font-medium py-2 px-4 rounded-lg transition-colors text-sm">
                        💡 Suggest Story
                    </button>
                    <button id="brainstorm-titles-btn" class="bg-cyan-600 hover:bg-cyan-500 text-white font-medium py-2 px-4 rounded-lg transition-colors text-sm">
                        📝 Brainstorm Titles
                    </button>
                </div>

                <!-- Manual Title Input -->
                <div class="mb-4">
                    <label for="manual-title-input" class="block text-sm font-medium mb-1">Story Title (Optional)</label>
                    <div class="flex space-x-2">
                        <input type="text" id="manual-title-input" class="flex-1 p-2 bg-slate-700 border border-slate-600 rounded-lg text-sm" placeholder="Enter your own title or use AI suggestions">
                        <button id="use-manual-title-btn" class="bg-indigo-600 hover:bg-indigo-500 text-white font-medium py-2 px-3 rounded-lg transition-colors text-sm whitespace-nowrap">
                            Use Title
                        </button>
                    </div>
                </div>

                <!-- Selected Title Display -->
                <div id="selected-title-display" class="p-3 bg-slate-900 rounded-lg hidden">
                    <div class="flex justify-between items-center">
                        <div>
                            <span class="text-sm text-slate-400">Selected Title:</span>
                            <div id="selected-title-text" class="font-medium text-cyan-400"></div>
                        </div>
                        <button id="clear-title-btn" class="text-red-400 hover:text-red-300 text-sm" title="Clear selected title">✕</button>
                    </div>
                </div>
                </div>
            </div>

            <div class="mt-4 space-y-3">
                <button id="preview-prompt-btn" class="w-full bg-slate-600 hover:bg-slate-500 text-white font-medium py-2 px-4 rounded-lg transition-colors">
                    👁️ Preview Final Prompt
                </button>
                <button id="generate-story-btn" class="w-full bg-green-600 hover:bg-green-500 text-white font-bold py-3 px-4 rounded-lg transition-colors text-lg">Generate Story</button>
            </div>
        </div>

        <!-- Right Column: AI Output -->
        <div id="ai-output-container" class="flex-1 p-6 flex flex-col column-container">
            <div class="flex justify-between items-center mb-4">
                <h2 class="text-2xl font-bold">Generated Story</h2>
                <button id="copy-story-btn" class="bg-slate-600 hover:bg-slate-500 text-white font-medium py-2 px-3 rounded-lg transition-colors hidden" title="Copy story to clipboard">
                    📋 Copy
                </button>
            </div>
            <div id="ai-output-content" class="flex-1 bg-slate-800 rounded-lg p-4 overflow-y-auto whitespace-pre-wrap">Your generated story will appear here...</div>
        </div>
    </div>

    <!-- Modal for adding/editing a brick -->
    <div id="modal-overlay" class="fixed inset-0 bg-black/70 flex items-center justify-center hidden z-50">
        <div id="brick-modal" class="bg-slate-800 rounded-lg p-8 shadow-xl w-full max-w-2xl max-h-[90vh] overflow-y-auto">
            <h2 id="modal-title" class="text-2xl font-bold mb-6">Create New Brick</h2>
            <form id="brick-form">
                <input type="hidden" id="block-type-input" value="">
                <input type="hidden" id="edit-block-id" value="">

                <div class="space-y-4">
                    <div>
                        <label for="block-name-input" class="block mb-1 font-semibold">Block Name</label>
                        <div class="flex space-x-2">
                            <input type="text" id="block-name-input" class="flex-1 p-2 bg-slate-700 border border-slate-600 rounded-lg" required>
                            <button type="button" id="generate-with-ai-btn" class="bg-purple-600 hover:bg-purple-500 text-white font-medium py-2 px-3 rounded-lg transition-colors whitespace-nowrap" title="Generate detailed description with AI">
                                🤖 Generate
                            </button>
                        </div>
                    </div>

                    <!-- Character Fields -->
                    <div id="character-fields" class="space-y-4 hidden">
                        <div>
                            <label for="character-archetype-input" class="block mb-1 font-semibold">Character's Archetype</label>
                            <input type="text" id="character-archetype-input" class="w-full p-2 bg-slate-700 border border-slate-600 rounded-lg" placeholder="e.g., The Rebel, The Mentor">
                        </div>
                        <div>
                            <label for="character-backstory-input" class="block mb-1 font-semibold">Character's Backstory</label>
                            <textarea id="character-backstory-input" class="w-full p-2 bg-slate-700 border border-slate-600 rounded-lg h-24"></textarea>
                        </div>
                        <div>
                            <label for="character-goal-input" class="block mb-1 font-semibold">Character's Goal</label>
                            <textarea id="character-goal-input" class="w-full p-2 bg-slate-700 border border-slate-600 rounded-lg h-24"></textarea>
                        </div>

                        <!-- Character AI Tools -->
                        <div class="bg-slate-900/50 p-3 rounded-lg">
                            <h5 class="font-semibold mb-2 text-sm">AI Character Tools</h5>
                            <div class="flex space-x-2">
                                <button type="button" id="deepen-character-btn" class="flex-1 bg-indigo-600 hover:bg-indigo-500 text-white font-medium py-2 px-3 rounded-lg transition-colors text-sm">
                                    🧠 Deepen Character
                                </button>
                                <button type="button" id="generate-dialogue-btn" class="flex-1 bg-teal-600 hover:bg-teal-500 text-white font-medium py-2 px-3 rounded-lg transition-colors text-sm">
                                    💬 Generate Dialogue
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Setting Fields -->
                    <div id="setting-fields" class="space-y-4 hidden">
                        <div>
                            <label for="setting-location-input" class="block mb-1 font-semibold">Location</label>
                            <input type="text" id="setting-location-input" class="w-full p-2 bg-slate-700 border border-slate-600 rounded-lg" placeholder="e.g., Ancient castle, Modern city">
                        </div>
                        <div>
                            <label for="setting-time-input" class="block mb-1 font-semibold">Time Period</label>
                            <input type="text" id="setting-time-input" class="w-full p-2 bg-slate-700 border border-slate-600 rounded-lg" placeholder="e.g., Medieval times, Present day">
                        </div>
                        <div>
                            <label for="setting-atmosphere-input" class="block mb-1 font-semibold">Atmosphere & Mood</label>
                            <textarea id="setting-atmosphere-input" class="w-full p-2 bg-slate-700 border border-slate-600 rounded-lg h-24" placeholder="Describe the mood, weather, lighting, sounds..."></textarea>
                        </div>
                    </div>

                    <!-- Plot Fields -->
                    <div id="plot-fields" class="space-y-4 hidden">
                        <div>
                            <label for="plot-conflict-input" class="block mb-1 font-semibold">Main Conflict</label>
                            <textarea id="plot-conflict-input" class="w-full p-2 bg-slate-700 border border-slate-600 rounded-lg h-24" placeholder="What is the central problem or challenge?"></textarea>
                        </div>
                        <div>
                            <label for="plot-inciting-input" class="block mb-1 font-semibold">Inciting Incident</label>
                            <textarea id="plot-inciting-input" class="w-full p-2 bg-slate-700 border border-slate-600 rounded-lg h-24" placeholder="What event kicks off the story?"></textarea>
                        </div>
                        <div>
                            <label for="plot-resolution-input" class="block mb-1 font-semibold">Resolution Direction</label>
                            <textarea id="plot-resolution-input" class="w-full p-2 bg-slate-700 border border-slate-600 rounded-lg h-24" placeholder="How should the conflict be resolved?"></textarea>
                        </div>
                    </div>

                    <!-- Theme Fields -->
                    <div id="theme-fields" class="space-y-4 hidden">
                        <div>
                            <label for="theme-message-input" class="block mb-1 font-semibold">Central Message</label>
                            <input type="text" id="theme-message-input" class="w-full p-2 bg-slate-700 border border-slate-600 rounded-lg" placeholder="e.g., Redemption, Love conquers all">
                        </div>
                        <div>
                            <label for="theme-tone-input" class="block mb-1 font-semibold">Tone</label>
                            <input type="text" id="theme-tone-input" class="w-full p-2 bg-slate-700 border border-slate-600 rounded-lg" placeholder="e.g., Hopeful, Dark, Humorous">
                        </div>
                        <div>
                            <label for="theme-symbolism-input" class="block mb-1 font-semibold">Symbolism & Motifs</label>
                            <textarea id="theme-symbolism-input" class="w-full p-2 bg-slate-700 border border-slate-600 rounded-lg h-24" placeholder="What symbols or recurring elements support the theme?"></textarea>
                        </div>
                    </div>
                </div>

                <div class="flex justify-end space-x-4 mt-8">
                    <button type="button" id="cancel-brick-btn" class="bg-slate-600 hover:bg-slate-500 text-white font-bold py-2 px-4 rounded-lg">Cancel</button>
                    <button type="submit" id="save-brick-btn" class="bg-indigo-600 hover:bg-indigo-500 text-white font-bold py-2 px-4 rounded-lg">Save Brick</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Modal for saving a story -->
    <div id="story-modal-overlay" class="fixed inset-0 bg-black/70 flex items-center justify-center hidden z-50">
        <div id="story-modal" class="bg-slate-800 rounded-lg p-8 shadow-xl w-full max-w-lg">
            <h2 id="story-modal-title" class="text-2xl font-bold mb-6">Save Story</h2>
            <form id="story-form">
                <input type="hidden" id="edit-story-id" value="">

                <div class="space-y-4">
                    <div>
                        <label for="story-title-input" class="block mb-1 font-semibold">Story Title</label>
                        <input type="text" id="story-title-input" class="w-full p-2 bg-slate-700 border border-slate-600 rounded-lg" required placeholder="Enter a title for your story">
                    </div>
                    <div>
                        <label for="story-description-input" class="block mb-1 font-semibold">Description (Optional)</label>
                        <textarea id="story-description-input" class="w-full p-2 bg-slate-700 border border-slate-600 rounded-lg h-24" placeholder="Brief description of your story..."></textarea>
                    </div>
                    <div class="bg-slate-900 p-3 rounded-lg">
                        <h4 class="font-semibold mb-2">Current Blocks:</h4>
                        <div id="story-blocks-preview" class="text-sm text-slate-300">
                            <!-- Will be populated by JavaScript -->
                        </div>
                    </div>
                </div>

                <div class="flex justify-end space-x-4 mt-8">
                    <button type="button" id="cancel-story-btn" class="bg-slate-600 hover:bg-slate-500 text-white font-bold py-2 px-4 rounded-lg">Cancel</button>
                    <button type="submit" id="save-story-btn" class="bg-green-600 hover:bg-green-500 text-white font-bold py-2 px-4 rounded-lg">Save Story</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Modal for AI results -->
    <div id="ai-results-modal-overlay" class="fixed inset-0 bg-black/70 flex items-center justify-center hidden z-50">
        <div id="ai-results-modal" class="bg-slate-800 rounded-lg p-8 shadow-xl w-full max-w-2xl max-h-[90vh] overflow-y-auto">
            <h2 id="ai-results-title" class="text-2xl font-bold mb-6">AI Results</h2>
            <div id="ai-results-content" class="space-y-4">
                <!-- AI results will be populated here -->
            </div>
            <div class="flex justify-end space-x-4 mt-8">
                <button type="button" id="close-ai-results-btn" class="bg-slate-600 hover:bg-slate-500 text-white font-bold py-2 px-4 rounded-lg">Close</button>
                <button type="button" id="apply-ai-results-btn" class="bg-indigo-600 hover:bg-indigo-500 text-white font-bold py-2 px-4 rounded-lg hidden">Apply</button>
            </div>
        </div>
    </div>

    <!-- Modal for prompt preview -->
    <div id="prompt-preview-modal-overlay" class="fixed inset-0 bg-black/70 flex items-center justify-center hidden z-50">
        <div id="prompt-preview-modal" class="bg-slate-800 rounded-lg p-8 shadow-xl w-full max-w-3xl max-h-[90vh] overflow-y-auto">
            <h2 class="text-2xl font-bold mb-6">Final Prompt Preview</h2>
            <div class="bg-slate-900 p-4 rounded-lg">
                <textarea id="final-prompt-preview" readonly class="w-full h-96 p-4 bg-slate-800 border border-slate-600 rounded-lg resize-none text-sm font-mono"></textarea>
            </div>
            <div class="flex justify-end space-x-4 mt-8">
                <button type="button" id="close-prompt-preview-btn" class="bg-slate-600 hover:bg-slate-500 text-white font-bold py-2 px-4 rounded-lg">Close</button>
                <button type="button" id="copy-prompt-btn" class="bg-indigo-600 hover:bg-indigo-500 text-white font-bold py-2 px-4 rounded-lg">📋 Copy Prompt</button>
            </div>
        </div>
    </div>

    </div>
</div>

<script>
    // Pass initial data from PHP to JavaScript
    const initialBricks = <?php echo json_encode($initialBricks); ?>;
    const initialStories = <?php echo json_encode($initialStories); ?>;
</script>
<script src="app/ai-story-builder/assets/js/app.js" type="module"></script>

<?php
// Include the dashboard footer
require_once __DIR__ . '/../../../templates/footer.php';
?>
