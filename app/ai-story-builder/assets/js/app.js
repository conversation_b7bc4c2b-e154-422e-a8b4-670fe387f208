document.addEventListener('DOMContentLoaded', () => {
    // --- STATE ---
    let state = {
        bricks: initialBricks || [],
        stories: initialStories || [],
        assembler: { Character: [], Setting: [], Plot: [], Theme: [] },
        currentCategory: 'all',
        currentLibrary: 'blocks',
        editingBrick: null,
        editingStory: null,
        selectedTitle: null
    };

    // --- SELECTORS ---
    const brickLibraryList = document.getElementById('brick-library-list');
    const storyLibraryList = document.getElementById('story-library-list');
    const blockLibraryView = document.getElementById('block-library-view');
    const storyLibraryView = document.getElementById('story-library-view');
    const addNewBrickBtn = document.getElementById('add-new-brick-btn');
    const saveCurrentStoryBtn = document.getElementById('save-current-story-btn');
    const brickTypeDropdown = document.getElementById('brick-type-dropdown');
    const modalOverlay = document.getElementById('modal-overlay');
    const modalTitle = document.getElementById('modal-title');
    const cancelBrickBtn = document.getElementById('cancel-brick-btn');
    const brickForm = document.getElementById('brick-form');
    const brickTypeInput = document.getElementById('brick-type-input');
    const editBrickIdInput = document.getElementById('edit-brick-id');
    const assemblerSlots = document.getElementById('prompt-assembler-slots');
    const finalPromptPreview = document.getElementById('final-prompt-preview');
    const generateStoryBtn = document.getElementById('generate-story-btn');
    const aiOutputContent = document.getElementById('ai-output-content');
    const copyStoryBtn = document.getElementById('copy-story-btn');
    const categoryTabs = document.querySelectorAll('.category-tab');
    const libraryTabs = document.querySelectorAll('.library-tab');

    // Story configuration selectors
    const storyLengthSelect = document.getElementById('story-length-select');
    const targetAudienceSelect = document.getElementById('target-audience-select');
    const continuationCheckbox = document.getElementById('continuation-checkbox');

    // Story modal selectors
    const storyModalOverlay = document.getElementById('story-modal-overlay');
    const storyModalTitle = document.getElementById('story-modal-title');
    const storyForm = document.getElementById('story-form');
    const storyTitleInput = document.getElementById('story-title-input');
    const storyDescriptionInput = document.getElementById('story-description-input');
    const storyBlocksPreview = document.getElementById('story-blocks-preview');
    const editStoryIdInput = document.getElementById('edit-story-id');
    const cancelStoryBtn = document.getElementById('cancel-story-btn');
    const saveStoryBtn = document.getElementById('save-story-btn');

    // AI assistance selectors
    const generateWithAIBtn = document.getElementById('generate-with-ai-btn');
    const deepenCharacterBtn = document.getElementById('deepen-character-btn');
    const generateDialogueBtn = document.getElementById('generate-dialogue-btn');
    const suggestStoryBtn = document.getElementById('suggest-story-btn');
    const brainstormTitlesBtn = document.getElementById('brainstorm-titles-btn');
    const selectedTitleDisplay = document.getElementById('selected-title-display');
    const selectedTitleText = document.getElementById('selected-title-text');
    const clearTitleBtn = document.getElementById('clear-title-btn');
    const manualTitleInput = document.getElementById('manual-title-input');
    const useManualTitleBtn = document.getElementById('use-manual-title-btn');
    const previewPromptBtn = document.getElementById('preview-prompt-btn');
    const generateDemoBricksBtn = document.getElementById('generate-demo-bricks-btn');

    // Prompt preview modal selectors
    const promptPreviewModalOverlay = document.getElementById('prompt-preview-modal-overlay');
    const closePromptPreviewBtn = document.getElementById('close-prompt-preview-btn');
    const copyPromptBtn = document.getElementById('copy-prompt-btn');

    // AI results modal selectors
    const aiResultsModalOverlay = document.getElementById('ai-results-modal-overlay');
    const aiResultsModal = document.getElementById('ai-results-modal');
    const aiResultsTitle = document.getElementById('ai-results-title');
    const aiResultsContent = document.getElementById('ai-results-content');
    const closeAIResultsBtn = document.getElementById('close-ai-results-btn');
    const applyAIResultsBtn = document.getElementById('apply-ai-results-btn');


    // --- RENDER FUNCTIONS ---
    const renderBrickLibrary = () => {
        brickLibraryList.innerHTML = '';

        // Filter bricks by current category
        const filteredBricks = state.currentCategory === 'all'
            ? state.bricks
            : state.bricks.filter(brick => brick.brickType === state.currentCategory);

        if (filteredBricks.length === 0) {
            const message = state.currentCategory === 'all'
                ? 'No bricks created yet. Click "+ New Brick" to start!'
                : `No ${state.currentCategory} bricks created yet.`;
            brickLibraryList.innerHTML = `<p class="text-slate-500 text-center">${message}</p>`;
            return;
        }

        filteredBricks.forEach(brick => {
            const card = document.createElement('div');
            card.className = 'bg-slate-700 p-4 rounded-lg hover:bg-slate-600 transition-colors relative group';
            card.setAttribute('draggable', 'true');
            card.dataset.brickId = brick.id;

            // Get type-specific color
            const typeColors = {
                'Character': 'text-blue-400',
                'Setting': 'text-green-400',
                'Plot': 'text-yellow-400',
                'Theme': 'text-purple-400'
            };

            card.innerHTML = `
                <div class="flex justify-between items-start mb-2">
                    <div class="flex-1 min-w-0">
                        <h3 class="font-bold text-lg truncate">${brick.brickName}</h3>
                        <p class="text-sm ${typeColors[brick.brickType] || 'text-indigo-400'}">${brick.brickType}</p>
                    </div>
                    <div class="flex space-x-1 opacity-0 group-hover:opacity-100 transition-opacity ml-2">
                        <button class="edit-brick-btn p-1 hover:bg-slate-500 rounded text-xs" data-brick-id="${brick.id}" title="Edit">✏️</button>
                        <button class="clone-brick-btn p-1 hover:bg-slate-500 rounded text-xs" data-brick-id="${brick.id}" title="Clone">📋</button>
                        <button class="delete-brick-btn p-1 hover:bg-red-600 rounded text-xs" data-brick-id="${brick.id}" title="Delete">🗑️</button>
                    </div>
                </div>
            `;
            brickLibraryList.appendChild(card);
        });
    };
    
    const renderAssembler = () => {
        Object.keys(state.assembler).forEach(slotType => {
            const slot = document.querySelector(`[data-slot-type="${slotType}"]`);
            const brickIds = state.assembler[slotType];

            if (brickIds.length > 0) {
                // Filter out any deleted bricks
                const validBricks = brickIds.map(id => state.bricks.find(b => b.id === id)).filter(Boolean);

                // Update state to remove any deleted bricks
                state.assembler[slotType] = validBricks.map(b => b.id);

                if (validBricks.length > 0) {
                    slot.className = 'min-h-24 bg-indigo-900/50 border-2 border-indigo-500 rounded-lg p-3 space-y-2';
                    slot.innerHTML = validBricks.map(brick => `
                        <div class="flex items-center justify-between bg-indigo-800/50 rounded p-2">
                            <div class="flex flex-col">
                                <h4 class="font-bold text-sm">${brick.brickName}</h4>
                                <p class="text-xs text-indigo-300">${brick.brickType}</p>
                            </div>
                            <button class="remove-from-assembler text-red-400 hover:text-red-300 text-lg" data-slot-type="${slotType}" data-brick-id="${brick.id}" title="Remove">×</button>
                        </div>
                    `).join('');
                } else {
                    slot.className = 'h-24 border-2 border-dashed border-slate-600 rounded-lg flex items-center justify-center text-slate-500';
                    slot.innerHTML = `Drop ${slotType} Brick Here`;
                }
            } else {
                slot.className = 'h-24 border-2 border-dashed border-slate-600 rounded-lg flex items-center justify-center text-slate-500';
                slot.innerHTML = `Drop ${slotType} Brick Here`;
            }
        });
        updatePromptPreview();
    };

    const updatePromptPreview = () => {
        let fullPrompt = '';

        // Add story configuration
        const storyLength = storyLengthSelect.value;
        const targetAudience = targetAudienceSelect.value;
        const isContinuation = continuationCheckbox.checked;

        // Add configuration to prompt
        fullPrompt += `Story Configuration:\n`;
        fullPrompt += `- Length: ${getStoryLengthDescription(storyLength)}\n`;
        fullPrompt += `- Target Audience: ${getTargetAudienceDescription(targetAudience)}\n`;
        if (isContinuation) {
            fullPrompt += `- Type: Continuation/Next Chapter\n`;
        }

        // Add selected title if available
        if (state.selectedTitle) {
            fullPrompt += `- Title: "${state.selectedTitle}"\n`;
        }
        fullPrompt += '\n';

        // Add story elements
        fullPrompt += 'Story Elements:\n';
        Object.entries(state.assembler).forEach(([type, brickIds]) => {
            if (brickIds.length > 0) {
                fullPrompt += `\n${type}s:\n`;
                brickIds.forEach(brickId => {
                    const brick = state.bricks.find(b => b.id === brickId);
                    if (brick) {
                        fullPrompt += `- ${brick.promptText}\n`;
                    }
                });
            }
        });

        finalPromptPreview.value = fullPrompt.trim();
    };

    const getStoryLengthDescription = (length) => {
        const descriptions = {
            'flash-fiction': 'Flash Fiction (100-1000 words)',
            'short-story': 'Short Story (1000-7500 words)',
            'novelette': 'Novelette (7500-17500 words)',
            'novel-chapter': 'Novel Chapter (2000-5000 words)'
        };
        return descriptions[length] || 'Short Story';
    };

    const getTargetAudienceDescription = (audience) => {
        const descriptions = {
            'children': 'Children (Ages 5-12)',
            'young-adult': 'Young Adult (Ages 13-17)',
            'adult': 'Adult (Ages 18+)'
        };
        return descriptions[audience] || 'Adult';
    };

    const renderCategoryTabs = () => {
        categoryTabs.forEach(tab => {
            const category = tab.dataset.category;
            if (category === state.currentCategory) {
                tab.classList.add('bg-indigo-600', 'text-white');
                tab.classList.remove('text-slate-400', 'hover:text-white');
            } else {
                tab.classList.remove('bg-indigo-600', 'text-white');
                tab.classList.add('text-slate-400', 'hover:text-white');
            }
        });
    };

    const renderLibraryTabs = () => {
        libraryTabs.forEach(tab => {
            const library = tab.dataset.library;
            if (library === state.currentLibrary) {
                tab.classList.add('bg-indigo-600', 'text-white');
                tab.classList.remove('text-slate-400', 'hover:text-white');
            } else {
                tab.classList.remove('bg-indigo-600', 'text-white');
                tab.classList.add('text-slate-400', 'hover:text-white');
            }
        });

        // Show/hide library views
        if (state.currentLibrary === 'blocks') {
            blockLibraryView.classList.remove('hidden');
            blockLibraryView.classList.add('flex');
            storyLibraryView.classList.add('hidden');
            storyLibraryView.classList.remove('flex');
        } else {
            blockLibraryView.classList.add('hidden');
            blockLibraryView.classList.remove('flex');
            storyLibraryView.classList.remove('hidden');
            storyLibraryView.classList.add('flex');
        }
    };

    const renderStoryLibrary = () => {
        storyLibraryList.innerHTML = '';

        if (state.stories.length === 0) {
            storyLibraryList.innerHTML = `<p class="text-slate-500 text-center">No stories saved yet. Create a story by adding blocks to the assembler and clicking "Save Current".</p>`;
            return;
        }

        // Sort stories by updated_at (most recent first)
        const sortedStories = [...state.stories].sort((a, b) =>
            new Date(b.updated_at) - new Date(a.updated_at)
        );

        sortedStories.forEach(story => {
            const card = document.createElement('div');
            card.className = 'bg-slate-700 p-4 rounded-lg hover:bg-slate-600 transition-colors relative group cursor-pointer';
            card.dataset.storyId = story.id;

            const blockCount = Object.values(story.assembler).reduce((total, blocks) => total + blocks.length, 0);
            const lastUpdated = new Date(story.updated_at).toLocaleDateString();

            card.innerHTML = `
                <div class="flex justify-between items-start mb-2">
                    <div class="flex-1 min-w-0">
                        <h3 class="font-bold text-lg truncate">${story.title}</h3>
                        <p class="text-sm text-slate-400">${blockCount} blocks • Updated ${lastUpdated}</p>
                        ${story.description ? `<p class="text-xs text-slate-500 mt-1 line-clamp-2">${story.description}</p>` : ''}
                    </div>
                    <div class="flex space-x-1 opacity-0 group-hover:opacity-100 transition-opacity ml-2">
                        <button class="edit-story-btn p-1 hover:bg-slate-500 rounded text-xs" data-story-id="${story.id}" title="Edit">✏️</button>
                        <button class="clone-story-btn p-1 hover:bg-slate-500 rounded text-xs" data-story-id="${story.id}" title="Clone">📋</button>
                        <button class="delete-story-btn p-1 hover:bg-red-600 rounded text-xs" data-story-id="${story.id}" title="Delete">🗑️</button>
                    </div>
                </div>
                <div class="text-xs text-slate-400">
                    ${Object.entries(story.assembler).map(([type, blocks]) =>
                        blocks.length > 0 ? `${blocks.length} ${type}${blocks.length > 1 ? 's' : ''}` : ''
                    ).filter(Boolean).join(' • ')}
                </div>
            `;

            storyLibraryList.appendChild(card);
        });
    };

    const showModal = (brickType, editBrick = null) => {
        state.editingBrick = editBrick;
        brickTypeInput.value = brickType;
        editBrickIdInput.value = editBrick ? editBrick.id : '';

        // Update modal title
        modalTitle.textContent = editBrick ? `Edit ${brickType} Brick` : `Create New ${brickType} Brick`;

        // Hide all field groups
        document.querySelectorAll('[id$="-fields"]').forEach(group => group.classList.add('hidden'));

        // Show relevant field group
        const fieldGroup = document.getElementById(`${brickType.toLowerCase()}-fields`);
        if (fieldGroup) {
            fieldGroup.classList.remove('hidden');
        }

        // Show/hide character-specific AI tools
        const characterTools = document.querySelectorAll('#deepen-character-btn, #generate-dialogue-btn');
        characterTools.forEach(btn => {
            if (brickType === 'Character') {
                btn.style.display = 'block';
            } else {
                btn.style.display = 'none';
            }
        });

        // Populate form if editing
        if (editBrick) {
            document.getElementById('brick-name-input').value = editBrick.brickName;
            populateFormFields(brickType, editBrick.data);
        } else {
            brickForm.reset();
            brickTypeInput.value = brickType;
        }

        modalOverlay.classList.remove('hidden');
    };

    const populateFormFields = (brickType, data) => {
        switch (brickType) {
            case 'Character':
                document.getElementById('character-archetype-input').value = data.archetype || '';
                document.getElementById('character-backstory-input').value = data.backstory || '';
                document.getElementById('character-goal-input').value = data.goal || '';
                break;
            case 'Setting':
                document.getElementById('setting-location-input').value = data.location || '';
                document.getElementById('setting-time-input').value = data.time || '';
                document.getElementById('setting-atmosphere-input').value = data.atmosphere || '';
                break;
            case 'Plot':
                document.getElementById('plot-conflict-input').value = data.conflict || '';
                document.getElementById('plot-inciting-input').value = data.inciting || '';
                document.getElementById('plot-resolution-input').value = data.resolution || '';
                break;
            case 'Theme':
                document.getElementById('theme-message-input').value = data.message || '';
                document.getElementById('theme-tone-input').value = data.tone || '';
                document.getElementById('theme-symbolism-input').value = data.symbolism || '';
                break;
        }
    };

    const showStoryModal = (editStory = null) => {
        state.editingStory = editStory;
        editStoryIdInput.value = editStory ? editStory.id : '';

        // Update modal title
        storyModalTitle.textContent = editStory ? 'Edit Story' : 'Save Story';

        // Populate form if editing
        if (editStory) {
            storyTitleInput.value = editStory.title;
            storyDescriptionInput.value = editStory.description || '';
        } else {
            storyForm.reset();
        }

        // Show current blocks preview
        updateStoryBlocksPreview();

        storyModalOverlay.classList.remove('hidden');
    };

    const updateStoryBlocksPreview = () => {
        const blockCounts = Object.entries(state.assembler).map(([type, blocks]) => {
            if (blocks.length > 0) {
                const blockNames = blocks.map(id => {
                    const brick = state.bricks.find(b => b.id === id);
                    return brick ? brick.brickName : 'Unknown';
                }).join(', ');
                return `<div><strong>${type}:</strong> ${blockNames}</div>`;
            }
            return '';
        }).filter(Boolean);

        // Add configuration info
        const configInfo = [
            `<div><strong>Length:</strong> ${getStoryLengthDescription(storyLengthSelect.value)}</div>`,
            `<div><strong>Audience:</strong> ${getTargetAudienceDescription(targetAudienceSelect.value)}</div>`
        ];

        if (continuationCheckbox.checked) {
            configInfo.push(`<div><strong>Type:</strong> Continuation/Next Chapter</div>`);
        }

        if (blockCounts.length === 0) {
            storyBlocksPreview.innerHTML = '<div class="text-slate-500">No blocks in assembler</div>';
        } else {
            storyBlocksPreview.innerHTML = configInfo.join('') + '<div class="mt-2 pt-2 border-t border-slate-600"></div>' + blockCounts.join('');
        }
    };

    const loadStoryIntoAssembler = (story) => {
        // Clear current assembler
        state.assembler = { Character: [], Setting: [], Plot: [], Theme: [] };

        // Load story's assembler state
        Object.keys(story.assembler).forEach(type => {
            if (story.assembler[type]) {
                state.assembler[type] = [...story.assembler[type]];
            }
        });

        // Re-render assembler and switch to blocks view
        renderAssembler();
        state.currentLibrary = 'blocks';
        renderLibraryTabs();
        renderBrickLibrary();
    };


    // --- API & DATA FUNCTIONS ---
    const saveNewBrick = async (newBrick) => {
        try {
            const response = await fetch('api/data.php', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ newBrick })
            });
            const result = await response.json();
            if (result.success) {
                state.bricks = result.bricks;
                renderBrickLibrary();
                renderAssembler();
            } else {
                console.error('Failed to save brick:', result.message);
                alert('Failed to save brick: ' + result.message);
            }
        } catch (error) {
            console.error('Error saving brick:', error);
            alert('Error saving brick. Please try again.');
        }
    };

    const updateBrick = async (brickId, updatedBrick) => {
        try {
            const response = await fetch('api/data.php', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ brickId, updateBrick: updatedBrick })
            });
            const result = await response.json();
            if (result.success) {
                state.bricks = result.bricks;
                renderBrickLibrary();
                renderAssembler();
            } else {
                console.error('Failed to update brick:', result.message);
                alert('Failed to update brick: ' + result.message);
            }
        } catch (error) {
            console.error('Error updating brick:', error);
            alert('Error updating brick. Please try again.');
        }
    };

    const deleteBrick = async (brickId) => {
        if (!confirm('Are you sure you want to delete this brick?')) return;

        try {
            const response = await fetch('api/data.php', {
                method: 'DELETE',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ brickId })
            });
            const result = await response.json();
            if (result.success) {
                state.bricks = result.bricks;
                // Remove from assembler if it was there
                Object.keys(state.assembler).forEach(slotType => {
                    state.assembler[slotType] = state.assembler[slotType].filter(id => id !== brickId);
                });
                renderBrickLibrary();
                renderAssembler();
            } else {
                console.error('Failed to delete brick:', result.message);
                alert('Failed to delete brick: ' + result.message);
            }
        } catch (error) {
            console.error('Error deleting brick:', error);
            alert('Error deleting brick. Please try again.');
        }
    };

    const cloneBrick = async (brickId) => {
        try {
            const response = await fetch('api/data.php', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ cloneBrick: brickId })
            });
            const result = await response.json();
            if (result.success) {
                state.bricks = result.bricks;
                renderBrickLibrary();
            } else {
                console.error('Failed to clone brick:', result.message);
                alert('Failed to clone brick: ' + result.message);
            }
        } catch (error) {
            console.error('Error cloning brick:', error);
            alert('Error cloning brick. Please try again.');
        }
    };

    // === STORY API FUNCTIONS ===

    const loadStories = async () => {
        try {
            const response = await fetch('api/stories.php');
            const result = await response.json();
            if (result.success) {
                state.stories = result.stories;
                renderStoryLibrary();
            } else {
                console.error('Failed to load stories:', result.message);
            }
        } catch (error) {
            console.error('Error loading stories:', error);
        }
    };

    const saveNewStory = async (newStory) => {
        try {
            const response = await fetch('api/stories.php', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ newStory })
            });
            const result = await response.json();
            if (result.success) {
                state.stories = result.stories;
                renderStoryLibrary();
            } else {
                console.error('Failed to save story:', result.message);
                alert('Failed to save story: ' + result.message);
            }
        } catch (error) {
            console.error('Error saving story:', error);
            alert('Error saving story. Please try again.');
        }
    };

    const updateStory = async (storyId, updatedStory) => {
        try {
            const response = await fetch('api/stories.php', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ storyId, updateStory: updatedStory })
            });
            const result = await response.json();
            if (result.success) {
                state.stories = result.stories;
                renderStoryLibrary();
            } else {
                console.error('Failed to update story:', result.message);
                alert('Failed to update story: ' + result.message);
            }
        } catch (error) {
            console.error('Error updating story:', error);
            alert('Error updating story. Please try again.');
        }
    };

    const deleteStory = async (storyId) => {
        if (!confirm('Are you sure you want to delete this story?')) return;

        try {
            const response = await fetch('api/stories.php', {
                method: 'DELETE',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ storyId })
            });
            const result = await response.json();
            if (result.success) {
                state.stories = result.stories;
                renderStoryLibrary();
            } else {
                console.error('Failed to delete story:', result.message);
                alert('Failed to delete story: ' + result.message);
            }
        } catch (error) {
            console.error('Error deleting story:', error);
            alert('Error deleting story. Please try again.');
        }
    };

    const cloneStory = async (storyId) => {
        try {
            const response = await fetch('api/stories.php', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ cloneStory: storyId })
            });
            const result = await response.json();
            if (result.success) {
                state.stories = result.stories;
                renderStoryLibrary();
            } else {
                console.error('Failed to clone story:', result.message);
                alert('Failed to clone story: ' + result.message);
            }
        } catch (error) {
            console.error('Error cloning story:', error);
            alert('Error cloning story. Please try again.');
        }
    };

    // === AI ASSISTANCE FUNCTIONS ===

    const callAIAssistance = async (action, data) => {
        try {
            const response = await fetch('api/ai-assist.php', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ action, data })
            });
            const result = await response.json();
            return result;
        } catch (error) {
            console.error('AI Assistance Error:', error);
            return { success: false, message: 'Failed to connect to AI service.' };
        }
    };

    const showAIResults = (title, content, applyCallback = null) => {
        aiResultsTitle.textContent = title;
        aiResultsContent.innerHTML = content;

        if (applyCallback) {
            applyAIResultsBtn.classList.remove('hidden');
            applyAIResultsBtn.onclick = () => {
                applyCallback();
                aiResultsModalOverlay.classList.add('hidden');
            };
        } else {
            applyAIResultsBtn.classList.add('hidden');
        }

        aiResultsModalOverlay.classList.remove('hidden');
    };

    const generateBlockWithAI = async () => {
        const blockName = document.getElementById('brick-name-input').value;
        const blockType = document.getElementById('brick-type-input').value;

        if (!blockName.trim()) {
            alert('Please enter a block name first.');
            return;
        }

        generateWithAIBtn.disabled = true;
        generateWithAIBtn.textContent = '🤖 Generating...';

        const result = await callAIAssistance('generate_block', {
            blockName: blockName,
            blockType: blockType
        });

        generateWithAIBtn.disabled = false;
        generateWithAIBtn.textContent = '🤖 Generate';

        if (result.success) {
            const content = `<div class="bg-slate-900 p-4 rounded-lg"><pre class="whitespace-pre-wrap text-sm">${result.content}</pre></div>`;
            showAIResults('AI Generated Content', content, () => {
                // Apply the generated content to the appropriate fields
                applyGeneratedContent(result.content, result.blockType);
            });
        } else {
            alert('Failed to generate content: ' + result.message);
        }
    };

    const applyGeneratedContent = (content, blockType) => {
        switch (blockType) {
            case 'Character':
                parseAndApplyCharacterContent(content);
                break;
            case 'Setting':
                parseAndApplySettingContent(content);
                break;
            case 'Plot':
                parseAndApplyPlotContent(content);
                break;
            case 'Theme':
                parseAndApplyThemeContent(content);
                break;
        }
    };

    const parseAndApplyCharacterContent = (content) => {
        // Clear existing content first
        document.getElementById('character-archetype-input').value = '';
        document.getElementById('character-backstory-input').value = '';
        document.getElementById('character-goal-input').value = '';

        // Try to parse structured content using section headers
        const sections = parseStructuredContent(content, ['ARCHETYPE', 'BACKSTORY', 'GOAL']);

        if (sections.ARCHETYPE || sections.BACKSTORY || sections.GOAL) {
            // Use structured content
            if (sections.ARCHETYPE) document.getElementById('character-archetype-input').value = sections.ARCHETYPE;
            if (sections.BACKSTORY) document.getElementById('character-backstory-input').value = sections.BACKSTORY;
            if (sections.GOAL) document.getElementById('character-goal-input').value = sections.GOAL;
        } else {
            // Fallback: try to parse by keywords in lines
            const lines = content.split('\n').filter(line => line.trim());
            let archetype = '';
            let backstory = '';
            let goal = '';

            for (let line of lines) {
                const cleanLine = line.replace(/^[-*•]\s*/, '').trim();
                const lowerLine = cleanLine.toLowerCase();

                if ((lowerLine.includes('archetype') || lowerLine.includes('type') || lowerLine.includes('role')) && !archetype) {
                    archetype = cleanLine.replace(/^[^:]*:?\s*/, '').trim();
                } else if ((lowerLine.includes('goal') || lowerLine.includes('objective') || lowerLine.includes('wants') || lowerLine.includes('motivation')) && !goal) {
                    goal = cleanLine.replace(/^[^:]*:?\s*/, '').trim();
                } else if ((lowerLine.includes('backstory') || lowerLine.includes('background') || lowerLine.includes('history') || lowerLine.includes('past')) && !backstory) {
                    backstory = cleanLine.replace(/^[^:]*:?\s*/, '').trim();
                }
            }

            // If still no structured content, use intelligent distribution
            if (!archetype && !backstory && !goal) {
                const sentences = content.split(/[.!?]+/).filter(s => s.trim());

                if (sentences.length >= 3) {
                    archetype = sentences[0].trim();
                    backstory = sentences.slice(1, -1).join('. ').trim() + '.';
                    goal = sentences[sentences.length - 1].trim() + '.';
                } else if (sentences.length === 2) {
                    archetype = sentences[0].trim() + '.';
                    backstory = sentences[1].trim() + '.';
                } else {
                    backstory = content;
                }
            }

            // Apply parsed content
            if (archetype) document.getElementById('character-archetype-input').value = archetype;
            if (backstory) document.getElementById('character-backstory-input').value = backstory;
            if (goal) document.getElementById('character-goal-input').value = goal;
        }
    };

    const parseStructuredContent = (content, sectionHeaders) => {
        const sections = {};
        const lines = content.split('\n');
        let currentSection = null;
        let currentContent = [];

        for (let line of lines) {
            const trimmedLine = line.trim();
            const upperLine = trimmedLine.toUpperCase();

            // Check if this line is a section header
            let foundHeader = null;
            for (let header of sectionHeaders) {
                if (upperLine.startsWith(header + ':') || upperLine === header) {
                    foundHeader = header;
                    break;
                }
            }

            if (foundHeader) {
                // Save previous section
                if (currentSection && currentContent.length > 0) {
                    sections[currentSection] = currentContent.join('\n').trim();
                }

                // Start new section
                currentSection = foundHeader;
                currentContent = [];

                // If there's content after the colon, include it
                const afterColon = trimmedLine.substring(foundHeader.length + 1).trim();
                if (afterColon) {
                    currentContent.push(afterColon);
                }
            } else if (currentSection && trimmedLine) {
                currentContent.push(trimmedLine);
            }
        }

        // Save last section
        if (currentSection && currentContent.length > 0) {
            sections[currentSection] = currentContent.join('\n').trim();
        }

        return sections;
    };

    const parseAndApplySettingContent = (content) => {
        // Clear existing content first
        document.getElementById('setting-location-input').value = '';
        document.getElementById('setting-time-input').value = '';
        document.getElementById('setting-atmosphere-input').value = '';

        // Try structured parsing first
        const sections = parseStructuredContent(content, ['LOCATION', 'TIME', 'TIME PERIOD', 'ATMOSPHERE']);

        if (sections.LOCATION || sections.TIME || sections['TIME PERIOD'] || sections.ATMOSPHERE) {
            if (sections.LOCATION) document.getElementById('setting-location-input').value = sections.LOCATION;
            if (sections.TIME || sections['TIME PERIOD']) document.getElementById('setting-time-input').value = sections.TIME || sections['TIME PERIOD'];
            if (sections.ATMOSPHERE) document.getElementById('setting-atmosphere-input').value = sections.ATMOSPHERE;
        } else {
            // Fallback parsing
            const lines = content.split('\n').filter(line => line.trim());
            let location = '';
            let time = '';
            let atmosphere = '';

            for (let line of lines) {
                const cleanLine = line.replace(/^[-*•]\s*/, '').trim();
                const lowerLine = cleanLine.toLowerCase();

                if ((lowerLine.includes('location') || lowerLine.includes('place') || lowerLine.includes('where')) && !location) {
                    location = cleanLine.replace(/^[^:]*:?\s*/, '').trim();
                } else if ((lowerLine.includes('time') || lowerLine.includes('period') || lowerLine.includes('era') || lowerLine.includes('when')) && !time) {
                    time = cleanLine.replace(/^[^:]*:?\s*/, '').trim();
                } else if ((lowerLine.includes('atmosphere') || lowerLine.includes('mood') || lowerLine.includes('feeling')) && !atmosphere) {
                    atmosphere = cleanLine.replace(/^[^:]*:?\s*/, '').trim();
                }
            }

            // Final fallback distribution
            if (!location && !time && !atmosphere) {
                const sentences = content.split(/[.!?]+/).filter(s => s.trim());
                if (sentences.length >= 2) {
                    location = sentences[0].trim() + '.';
                    atmosphere = sentences.slice(1).join('. ').trim() + '.';
                } else {
                    atmosphere = content;
                }
            }

            if (location) document.getElementById('setting-location-input').value = location;
            if (time) document.getElementById('setting-time-input').value = time;
            if (atmosphere) document.getElementById('setting-atmosphere-input').value = atmosphere;
        }
    };

    const parseAndApplyPlotContent = (content) => {
        const lines = content.split('\n').filter(line => line.trim());

        let conflict = '';
        let inciting = '';
        let resolution = '';

        for (let line of lines) {
            const lowerLine = line.toLowerCase();
            if (lowerLine.includes('conflict') || lowerLine.includes('problem') || lowerLine.includes('tension')) {
                conflict = line.replace(/^[^:]*:?\s*/, '').trim();
            } else if (lowerLine.includes('inciting') || lowerLine.includes('catalyst') || lowerLine.includes('trigger')) {
                inciting = line.replace(/^[^:]*:?\s*/, '').trim();
            } else if (lowerLine.includes('resolution') || lowerLine.includes('ending') || lowerLine.includes('conclusion')) {
                resolution = line.replace(/^[^:]*:?\s*/, '').trim();
            }
        }

        // Fallback distribution
        if (!conflict && !inciting && !resolution) {
            const sentences = content.split(/[.!?]+/).filter(s => s.trim());
            if (sentences.length >= 3) {
                conflict = sentences[0].trim();
                inciting = sentences[1].trim();
                resolution = sentences.slice(2).join('. ').trim();
            } else {
                conflict = content;
            }
        }

        if (conflict) document.getElementById('plot-conflict-input').value = conflict;
        if (inciting) document.getElementById('plot-inciting-input').value = inciting;
        if (resolution) document.getElementById('plot-resolution-input').value = resolution;
    };

    const parseAndApplyThemeContent = (content) => {
        const lines = content.split('\n').filter(line => line.trim());

        let message = '';
        let tone = '';
        let symbolism = '';

        for (let line of lines) {
            const lowerLine = line.toLowerCase();
            if (lowerLine.includes('message') || lowerLine.includes('meaning') || lowerLine.includes('theme')) {
                message = line.replace(/^[^:]*:?\s*/, '').trim();
            } else if (lowerLine.includes('tone') || lowerLine.includes('mood') || lowerLine.includes('style')) {
                tone = line.replace(/^[^:]*:?\s*/, '').trim();
            } else if (lowerLine.includes('symbol') || lowerLine.includes('motif') || lowerLine.includes('metaphor')) {
                symbolism = line.replace(/^[^:]*:?\s*/, '').trim();
            }
        }

        // Fallback distribution
        if (!message && !tone && !symbolism) {
            const sentences = content.split(/[.!?]+/).filter(s => s.trim());
            if (sentences.length >= 2) {
                message = sentences[0].trim();
                symbolism = sentences.slice(1).join('. ').trim();
            } else {
                symbolism = content;
            }
        }

        if (message) document.getElementById('theme-message-input').value = message;
        if (tone) document.getElementById('theme-tone-input').value = tone;
        if (symbolism) document.getElementById('theme-symbolism-input').value = symbolism;
    };

    const deepenCharacter = async () => {
        const characterData = {
            name: document.getElementById('brick-name-input').value,
            archetype: document.getElementById('character-archetype-input').value,
            backstory: document.getElementById('character-backstory-input').value,
            goal: document.getElementById('character-goal-input').value
        };

        if (!characterData.name.trim()) {
            alert('Please enter a character name first.');
            return;
        }

        deepenCharacterBtn.disabled = true;
        deepenCharacterBtn.textContent = '🧠 Deepening...';

        const result = await callAIAssistance('deepen_character', {
            character: characterData
        });

        deepenCharacterBtn.disabled = false;
        deepenCharacterBtn.textContent = '🧠 Deepen Character';

        if (result.success) {
            const content = `<div class="bg-slate-900 p-4 rounded-lg"><pre class="whitespace-pre-wrap text-sm">${result.content}</pre></div>`;
            showAIResults('Character Depth', content, () => {
                // Append the depth information to backstory
                const currentBackstory = document.getElementById('character-backstory-input').value;
                const newBackstory = currentBackstory + (currentBackstory ? '\n\n' : '') + result.content;
                document.getElementById('character-backstory-input').value = newBackstory;
            });
        } else {
            alert('Failed to deepen character: ' + result.message);
        }
    };

    const generateDialogue = async () => {
        const characterData = {
            name: document.getElementById('brick-name-input').value,
            archetype: document.getElementById('character-archetype-input').value,
            backstory: document.getElementById('character-backstory-input').value,
            goal: document.getElementById('character-goal-input').value
        };

        if (!characterData.name.trim()) {
            alert('Please enter a character name first.');
            return;
        }

        generateDialogueBtn.disabled = true;
        generateDialogueBtn.textContent = '💬 Generating...';

        const result = await callAIAssistance('generate_dialogue', {
            character: characterData
        });

        generateDialogueBtn.disabled = false;
        generateDialogueBtn.textContent = '💬 Generate Dialogue';

        if (result.success) {
            const content = `<div class="bg-slate-900 p-4 rounded-lg"><pre class="whitespace-pre-wrap text-sm">${result.content}</pre></div>`;
            showAIResults('Sample Dialogue', content);
        } else {
            alert('Failed to generate dialogue: ' + result.message);
        }
    };

    const suggestStory = async () => {
        if (state.bricks.length === 0) {
            alert('Please create some blocks first to get story suggestions.');
            return;
        }

        suggestStoryBtn.disabled = true;
        suggestStoryBtn.textContent = '💡 Suggesting...';

        const result = await callAIAssistance('suggest_story', {
            bricks: state.bricks
        });

        suggestStoryBtn.disabled = false;
        suggestStoryBtn.textContent = '💡 Suggest Story';

        if (result.success && result.suggestion) {
            const suggestion = result.suggestion;
            const content = `
                <div class="bg-slate-900 p-4 rounded-lg space-y-3">
                    <h3 class="text-lg font-bold text-indigo-400">${suggestion.title}</h3>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-3 text-sm">
                        <div><strong>Character:</strong> ${suggestion.character}</div>
                        <div><strong>Setting:</strong> ${suggestion.setting}</div>
                        <div><strong>Plot:</strong> ${suggestion.plot}</div>
                    </div>
                    <div class="text-sm text-slate-300">
                        <strong>Why this works:</strong> ${suggestion.reason}
                    </div>
                </div>
            `;
            showAIResults('Story Suggestion', content, () => {
                // Apply the suggestion to the assembler
                applySuggestion(suggestion);
            });
        } else {
            alert('Failed to generate story suggestion: ' + (result.message || 'Unknown error'));
        }
    };

    const applySuggestion = (suggestion) => {
        // Clear current assembler
        state.assembler = { Character: [], Setting: [], Plot: [], Theme: [] };

        // Find and add suggested bricks
        const characterBrick = state.bricks.find(b => b.brickName === suggestion.character);
        const settingBrick = state.bricks.find(b => b.brickName === suggestion.setting);
        const plotBrick = state.bricks.find(b => b.brickName === suggestion.plot);

        if (characterBrick) state.assembler.Character.push(characterBrick.id);
        if (settingBrick) state.assembler.Setting.push(settingBrick.id);
        if (plotBrick) state.assembler.Plot.push(plotBrick.id);

        // Switch to blocks view and re-render
        state.currentLibrary = 'blocks';
        renderLibraryTabs();
        renderAssembler();
        renderBrickLibrary();
    };

    const brainstormTitles = async () => {
        // Check if there are blocks in assembler
        const hasBlocks = Object.values(state.assembler).some(blocks => blocks.length > 0);
        if (!hasBlocks) {
            alert('Please add some blocks to the assembler first.');
            return;
        }

        brainstormTitlesBtn.disabled = true;
        brainstormTitlesBtn.textContent = '📝 Brainstorming...';

        const result = await callAIAssistance('brainstorm_titles', {
            assembler: state.assembler,
            bricks: state.bricks
        });

        brainstormTitlesBtn.disabled = false;
        brainstormTitlesBtn.textContent = '📝 Brainstorm Titles';

        if (result.success) {
            // Parse titles and create clickable options
            const titles = parseTitlesFromResponse(result.titles);
            const content = createTitleSelectionContent(titles);
            showAIResults('Story Title Ideas', content);
        } else {
            alert('Failed to brainstorm titles: ' + result.message);
        }
    };

    const parseTitlesFromResponse = (response) => {
        // Extract titles from numbered list or similar format
        const lines = response.split('\n').filter(line => line.trim());
        const titles = [];

        for (let line of lines) {
            // Remove numbering, bullets, or other prefixes
            const cleanTitle = line.replace(/^\d+\.?\s*/, '')
                                  .replace(/^[-*•]\s*/, '')
                                  .replace(/^["'"]/, '')
                                  .replace(/["'"]$/, '')
                                  .trim();

            if (cleanTitle && cleanTitle.length > 3) {
                titles.push(cleanTitle);
            }
        }

        return titles.slice(0, 5); // Limit to 5 titles
    };

    const createTitleSelectionContent = (titles) => {
        if (titles.length === 0) {
            return '<div class="bg-slate-900 p-4 rounded-lg">No titles could be parsed from the response.</div>';
        }

        let content = '<div class="bg-slate-900 p-4 rounded-lg space-y-3">';
        content += '<p class="text-sm text-slate-300 mb-4">Click on a title to use it for your story:</p>';

        titles.forEach((title, index) => {
            content += `
                <div class="title-option p-3 bg-slate-800 hover:bg-slate-700 rounded-lg cursor-pointer transition-colors border border-slate-600 hover:border-indigo-500"
                     data-title="${title.replace(/"/g, '&quot;')}"
                     onclick="selectTitle('${title.replace(/'/g, "\\'")}')">
                    <div class="font-medium text-indigo-400">${title}</div>
                </div>
            `;
        });

        content += '</div>';
        return content;
    };

    // Make selectTitle available globally for onclick
    window.selectTitle = (title) => {
        state.selectedTitle = title;

        // Update the AI results modal to show selection
        const titleOptions = document.querySelectorAll('.title-option');
        titleOptions.forEach(option => {
            option.classList.remove('bg-indigo-900', 'border-indigo-400');
            option.classList.add('bg-slate-800', 'border-slate-600');
        });

        // Highlight selected title
        const selectedOption = document.querySelector(`[data-title="${title.replace(/"/g, '&quot;')}"]`);
        if (selectedOption) {
            selectedOption.classList.remove('bg-slate-800', 'border-slate-600');
            selectedOption.classList.add('bg-indigo-900', 'border-indigo-400');
        }

        // Show apply button
        applyAIResultsBtn.classList.remove('hidden');
        applyAIResultsBtn.textContent = 'Use Selected Title';
        applyAIResultsBtn.onclick = () => {
            applySelectedTitle(title);
            aiResultsModalOverlay.classList.add('hidden');
        };
    };

    const applySelectedTitle = (title) => {
        // Store the selected title for use in story generation
        state.selectedTitle = title;

        // Update the title display
        updateSelectedTitleDisplay();

        // Update the prompt preview to include the title
        updatePromptPreview();

        // Show confirmation
        alert(`Title "${title}" will be used for your next story generation.`);
    };

    const updateSelectedTitleDisplay = () => {
        if (state.selectedTitle) {
            selectedTitleText.textContent = state.selectedTitle;
            selectedTitleDisplay.classList.remove('hidden');
        } else {
            selectedTitleDisplay.classList.add('hidden');
        }
    };

    const clearSelectedTitle = () => {
        state.selectedTitle = null;
        manualTitleInput.value = '';
        updateSelectedTitleDisplay();
        updatePromptPreview();
    };

    const useManualTitle = () => {
        const title = manualTitleInput.value.trim();
        if (!title) {
            alert('Please enter a title first.');
            return;
        }

        state.selectedTitle = title;
        updateSelectedTitleDisplay();
        updatePromptPreview();

        // Clear the input field
        manualTitleInput.value = '';
    };

    const showPromptPreview = () => {
        updatePromptPreview();
        promptPreviewModalOverlay.classList.remove('hidden');
    };

    const copyPromptToClipboard = async () => {
        try {
            await navigator.clipboard.writeText(finalPromptPreview.value);

            // Visual feedback
            const originalText = copyPromptBtn.textContent;
            copyPromptBtn.textContent = '✓ Copied!';
            copyPromptBtn.classList.remove('bg-indigo-600', 'hover:bg-indigo-500');
            copyPromptBtn.classList.add('bg-green-600', 'hover:bg-green-500');

            setTimeout(() => {
                copyPromptBtn.textContent = originalText;
                copyPromptBtn.classList.remove('bg-green-600', 'hover:bg-green-500');
                copyPromptBtn.classList.add('bg-indigo-600', 'hover:bg-indigo-500');
            }, 2000);
        } catch (error) {
            console.error('Failed to copy prompt:', error);
            alert('Failed to copy prompt to clipboard. Please select and copy manually.');
        }
    };

    const generateDemoBricks = async () => {
        if (state.bricks.length > 0) {
            if (!confirm('This will add demo bricks to your library. Continue?')) {
                return;
            }
        }

        generateDemoBricksBtn.disabled = true;
        generateDemoBricksBtn.textContent = '🎲 Generating...';

        const demoBricks = [
            {
                brickType: "Character",
                brickName: "Elena Shadowmere",
                data: {
                    archetype: "The Reluctant Hero",
                    backstory: "A former royal guard who fled the kingdom after witnessing corruption in the court. Now works as a blacksmith in a remote village, hiding her true identity and exceptional sword skills.",
                    goal: "To find redemption by protecting the innocent while avoiding her past catching up with her."
                },
                promptText: "Character: Elena Shadowmere is a The Reluctant Hero. Background: A former royal guard who fled the kingdom after witnessing corruption in the court. Now works as a blacksmith in a remote village, hiding her true identity and exceptional sword skills. Primary goal: To find redemption by protecting the innocent while avoiding her past catching up with her."
            },
            {
                brickType: "Character",
                brickName: "Marcus the Wanderer",
                data: {
                    archetype: "The Wise Mentor",
                    backstory: "An ancient mage who has lived for centuries, traveling between worlds and collecting knowledge. He appears as a humble traveler but possesses immense magical power.",
                    goal: "To guide young heroes on their journeys while searching for a way to break an ancient curse that binds him to eternal wandering."
                },
                promptText: "Character: Marcus the Wanderer is a The Wise Mentor. Background: An ancient mage who has lived for centuries, traveling between worlds and collecting knowledge. He appears as a humble traveler but possesses immense magical power. Primary goal: To guide young heroes on their journeys while searching for a way to break an ancient curse that binds him to eternal wandering."
            },
            {
                brickType: "Setting",
                brickName: "The Floating City of Aethros",
                data: {
                    location: "A magnificent city suspended in the clouds by ancient magic",
                    time: "A fantasy realm where magic and technology coexist",
                    atmosphere: "Ethereal and mystical, with floating bridges connecting crystal spires. The air shimmers with magical energy, and the sound of wind chimes echoes through the streets. Below, clouds drift lazily, hiding the world beneath."
                },
                promptText: "Setting: The story takes place in A magnificent city suspended in the clouds by ancient magic during A fantasy realm where magic and technology coexist. Atmosphere: Ethereal and mystical, with floating bridges connecting crystal spires. The air shimmers with magical energy, and the sound of wind chimes echoes through the streets. Below, clouds drift lazily, hiding the world beneath."
            },
            {
                brickType: "Setting",
                brickName: "The Whispering Woods",
                data: {
                    location: "An ancient forest where the trees themselves are sentient",
                    time: "Timeless - exists outside normal temporal flow",
                    atmosphere: "Mysterious and alive, where shadows dance between massive tree trunks. The forest whispers secrets in languages long forgotten, and paths shift when no one is watching. Bioluminescent fungi provide an eerie blue-green glow."
                },
                promptText: "Setting: The story takes place in An ancient forest where the trees themselves are sentient during Timeless - exists outside normal temporal flow. Atmosphere: Mysterious and alive, where shadows dance between massive tree trunks. The forest whispers secrets in languages long forgotten, and paths shift when no one is watching. Bioluminescent fungi provide an eerie blue-green glow."
            },
            {
                brickType: "Plot",
                brickName: "The Stolen Crown",
                data: {
                    conflict: "The kingdom's magical crown has been stolen, causing the protective barriers around the realm to weaken",
                    inciting: "During the coronation ceremony, shadowy figures infiltrate the palace and steal the crown, leaving behind only a cryptic message",
                    resolution: "The heroes must track down the thieves through dangerous territories, uncover a conspiracy, and retrieve the crown before the kingdom falls to ancient evils"
                },
                promptText: "Plot: Main conflict - The kingdom's magical crown has been stolen, causing the protective barriers around the realm to weaken. Inciting incident: During the coronation ceremony, shadowy figures infiltrate the palace and steal the crown, leaving behind only a cryptic message. Resolution direction: The heroes must track down the thieves through dangerous territories, uncover a conspiracy, and retrieve the crown before the kingdom falls to ancient evils."
            },
            {
                brickType: "Plot",
                brickName: "The Last Library",
                data: {
                    conflict: "Knowledge itself is disappearing from the world as books, memories, and even thoughts begin to fade away",
                    inciting: "The protagonist discovers they are immune to the forgetting plague and finds a hidden library that may hold the key to saving all knowledge",
                    resolution: "A race against time to decode ancient texts and perform a ritual that will restore the world's memories before civilization collapses into ignorance"
                },
                promptText: "Plot: Main conflict - Knowledge itself is disappearing from the world as books, memories, and even thoughts begin to fade away. Inciting incident: The protagonist discovers they are immune to the forgetting plague and finds a hidden library that may hold the key to saving all knowledge. Resolution direction: A race against time to decode ancient texts and perform a ritual that will restore the world's memories before civilization collapses into ignorance."
            },
            {
                brickType: "Theme",
                brickName: "Redemption Through Sacrifice",
                data: {
                    message: "True redemption requires giving up what we value most for the greater good",
                    tone: "Bittersweet and hopeful",
                    symbolism: "Broken chains representing freedom from past mistakes, dawn breaking through darkness symbolizing new beginnings, and bridges representing connections between past and future"
                },
                promptText: "Theme: Central message - True redemption requires giving up what we value most for the greater good. Tone: Bittersweet and hopeful. Symbolism: Broken chains representing freedom from past mistakes, dawn breaking through darkness symbolizing new beginnings, and bridges representing connections between past and future."
            },
            {
                brickType: "Theme",
                brickName: "The Power of Knowledge",
                data: {
                    message: "Knowledge is the most powerful force in the universe, but it must be shared to truly matter",
                    tone: "Inspiring and thoughtful",
                    symbolism: "Light piercing darkness represents enlightenment, books as vessels of immortality, and seeds representing how knowledge grows when planted in fertile minds"
                },
                promptText: "Theme: Central message - Knowledge is the most powerful force in the universe, but it must be shared to truly matter. Tone: Inspiring and thoughtful. Symbolism: Light piercing darkness represents enlightenment, books as vessels of immortality, and seeds representing how knowledge grows when planted in fertile minds."
            }
        ];

        // Add demo bricks to state and save
        for (let demoBrick of demoBricks) {
            demoBrick.id = uniqid('brick_');
            state.bricks.push(demoBrick);
        }

        try {
            const response = await fetch('api/data.php', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ newBrick: demoBricks[0] }) // This will trigger a save
            });

            // Save all demo bricks
            for (let i = 1; i < demoBricks.length; i++) {
                await fetch('api/data.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ newBrick: demoBricks[i] })
                });
            }

            // Reload bricks from server to get the saved versions
            const loadResponse = await fetch('api/data.php');
            const loadResult = await loadResponse.json();
            if (loadResult.success) {
                state.bricks = loadResult.bricks;
                renderBrickLibrary();
            }

            alert('Demo bricks generated! You now have example characters, settings, plots, and themes to experiment with.');

        } catch (error) {
            console.error('Error generating demo bricks:', error);
            alert('Failed to generate demo bricks. Please try again.');
        }

        generateDemoBricksBtn.disabled = false;
        generateDemoBricksBtn.textContent = '🎲 Demo Bricks';
    };

    // Helper function to generate unique IDs
    const uniqid = (prefix = '') => {
        return prefix + Date.now().toString(36) + Math.random().toString(36).substr(2);
    };


    // --- EVENT LISTENERS ---
    // Dropdown for new brick types
    addNewBrickBtn.addEventListener('click', (e) => {
        e.stopPropagation();
        brickTypeDropdown.classList.toggle('hidden');
    });

    // Hide dropdown when clicking outside
    document.addEventListener('click', () => {
        brickTypeDropdown.classList.add('hidden');
    });

    // Brick type selection
    brickTypeDropdown.addEventListener('click', (e) => {
        if (e.target.dataset.brickType) {
            const brickType = e.target.dataset.brickType;
            showModal(brickType);
            brickTypeDropdown.classList.add('hidden');
        }
    });

    // Library tabs
    libraryTabs.forEach(tab => {
        tab.addEventListener('click', () => {
            state.currentLibrary = tab.dataset.library;
            renderLibraryTabs();
            if (state.currentLibrary === 'stories') {
                loadStories();
            }
        });
    });

    // Category tabs
    categoryTabs.forEach(tab => {
        tab.addEventListener('click', () => {
            state.currentCategory = tab.dataset.category;
            renderCategoryTabs();
            renderBrickLibrary();
        });
    });

    // Modal controls
    cancelBrickBtn.addEventListener('click', () => {
        modalOverlay.classList.add('hidden');
        state.editingBrick = null;
    });

    modalOverlay.addEventListener('click', (e) => {
        if (e.target === modalOverlay) {
            modalOverlay.classList.add('hidden');
            state.editingBrick = null;
        }
    });

    // Story modal controls
    saveCurrentStoryBtn.addEventListener('click', () => {
        // Check if there are blocks in assembler
        const hasBlocks = Object.values(state.assembler).some(blocks => blocks.length > 0);
        if (!hasBlocks) {
            alert('Please add some blocks to the assembler before saving a story.');
            return;
        }
        showStoryModal();
    });

    cancelStoryBtn.addEventListener('click', () => {
        storyModalOverlay.classList.add('hidden');
        state.editingStory = null;
    });

    storyModalOverlay.addEventListener('click', (e) => {
        if (e.target === storyModalOverlay) {
            storyModalOverlay.classList.add('hidden');
            state.editingStory = null;
        }
    });

    // AI assistance event listeners
    generateWithAIBtn.addEventListener('click', generateBlockWithAI);
    deepenCharacterBtn.addEventListener('click', deepenCharacter);
    generateDialogueBtn.addEventListener('click', generateDialogue);
    suggestStoryBtn.addEventListener('click', suggestStory);
    brainstormTitlesBtn.addEventListener('click', brainstormTitles);
    clearTitleBtn.addEventListener('click', clearSelectedTitle);
    useManualTitleBtn.addEventListener('click', useManualTitle);
    previewPromptBtn.addEventListener('click', showPromptPreview);
    generateDemoBricksBtn.addEventListener('click', generateDemoBricks);

    // Manual title input - allow Enter key
    manualTitleInput.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') {
            useManualTitle();
        }
    });

    // AI results modal controls
    closeAIResultsBtn.addEventListener('click', () => {
        aiResultsModalOverlay.classList.add('hidden');
    });

    aiResultsModalOverlay.addEventListener('click', (e) => {
        if (e.target === aiResultsModalOverlay) {
            aiResultsModalOverlay.classList.add('hidden');
        }
    });

    // Prompt preview modal controls
    closePromptPreviewBtn.addEventListener('click', () => {
        promptPreviewModalOverlay.classList.add('hidden');
    });

    copyPromptBtn.addEventListener('click', copyPromptToClipboard);

    promptPreviewModalOverlay.addEventListener('click', (e) => {
        if (e.target === promptPreviewModalOverlay) {
            promptPreviewModalOverlay.classList.add('hidden');
        }
    });

    // Form submission
    brickForm.addEventListener('submit', (e) => {
        e.preventDefault();

        const brickType = brickTypeInput.value;
        const brickName = document.getElementById('brick-name-input').value;
        const editBrickId = editBrickIdInput.value;

        let data = {};
        let promptText = '';

        // Collect data based on brick type
        switch (brickType) {
            case 'Character':
                data = {
                    archetype: document.getElementById('character-archetype-input').value,
                    backstory: document.getElementById('character-backstory-input').value,
                    goal: document.getElementById('character-goal-input').value
                };
                promptText = `Character: ${brickName} is a ${data.archetype}. Background: ${data.backstory}. Primary goal: ${data.goal}.`;
                break;

            case 'Setting':
                data = {
                    location: document.getElementById('setting-location-input').value,
                    time: document.getElementById('setting-time-input').value,
                    atmosphere: document.getElementById('setting-atmosphere-input').value
                };
                promptText = `Setting: The story takes place in ${data.location} during ${data.time}. Atmosphere: ${data.atmosphere}.`;
                break;

            case 'Plot':
                data = {
                    conflict: document.getElementById('plot-conflict-input').value,
                    inciting: document.getElementById('plot-inciting-input').value,
                    resolution: document.getElementById('plot-resolution-input').value
                };
                promptText = `Plot: Main conflict - ${data.conflict}. Inciting incident: ${data.inciting}. Resolution direction: ${data.resolution}.`;
                break;

            case 'Theme':
                data = {
                    message: document.getElementById('theme-message-input').value,
                    tone: document.getElementById('theme-tone-input').value,
                    symbolism: document.getElementById('theme-symbolism-input').value
                };
                promptText = `Theme: Central message - ${data.message}. Tone: ${data.tone}. Symbolism: ${data.symbolism}.`;
                break;
        }

        const brickData = {
            brickType,
            brickName,
            data,
            promptText
        };

        if (editBrickId) {
            // Update existing brick
            updateBrick(editBrickId, brickData);
        } else {
            // Create new brick
            saveNewBrick(brickData);
        }

        brickForm.reset();
        modalOverlay.classList.add('hidden');
        state.editingBrick = null;
    });

    // Story form submission
    storyForm.addEventListener('submit', (e) => {
        e.preventDefault();

        const storyTitle = storyTitleInput.value;
        const storyDescription = storyDescriptionInput.value;
        const editStoryId = editStoryIdInput.value;

        const storyData = {
            title: storyTitle,
            description: storyDescription,
            assembler: JSON.parse(JSON.stringify(state.assembler)) // Deep copy
        };

        if (editStoryId) {
            // Update existing story
            updateStory(editStoryId, storyData);
        } else {
            // Create new story
            saveNewStory(storyData);
        }

        storyForm.reset();
        storyModalOverlay.classList.add('hidden');
        state.editingStory = null;
    });

    // Brick action buttons
    brickLibraryList.addEventListener('click', (e) => {
        const brickId = e.target.dataset.brickId;
        if (!brickId) return;

        if (e.target.classList.contains('edit-brick-btn')) {
            e.stopPropagation();
            const brick = state.bricks.find(b => b.id === brickId);
            if (brick) {
                showModal(brick.brickType, brick);
            }
        } else if (e.target.classList.contains('clone-brick-btn')) {
            e.stopPropagation();
            cloneBrick(brickId);
        } else if (e.target.classList.contains('delete-brick-btn')) {
            e.stopPropagation();
            deleteBrick(brickId);
        }
    });

    // Story action buttons
    storyLibraryList.addEventListener('click', (e) => {
        const storyId = e.target.dataset.storyId;
        if (!storyId) {
            // Check if clicked on story card itself (load story)
            const storyCard = e.target.closest('[data-story-id]');
            if (storyCard && !e.target.closest('button')) {
                const clickedStoryId = storyCard.dataset.storyId;
                const story = state.stories.find(s => s.id === clickedStoryId);
                if (story) {
                    loadStoryIntoAssembler(story);
                }
            }
            return;
        }

        if (e.target.classList.contains('edit-story-btn')) {
            e.stopPropagation();
            const story = state.stories.find(s => s.id === storyId);
            if (story) {
                showStoryModal(story);
            }
        } else if (e.target.classList.contains('clone-story-btn')) {
            e.stopPropagation();
            cloneStory(storyId);
        } else if (e.target.classList.contains('delete-story-btn')) {
            e.stopPropagation();
            deleteStory(storyId);
        }
    });

    // Drag and Drop
    brickLibraryList.addEventListener('dragstart', (e) => {
        if (e.target.dataset.brickId) {
            e.dataTransfer.setData('text/plain', e.target.dataset.brickId);
        }
    });

    assemblerSlots.addEventListener('dragover', (e) => {
        e.preventDefault();
    });

    assemblerSlots.addEventListener('drop', (e) => {
        e.preventDefault();
        const slot = e.target.closest('[data-slot-type]');
        if (!slot) return;

        const brickId = e.dataTransfer.getData('text/plain');
        const brick = state.bricks.find(b => b.id === brickId);
        const slotType = slot.dataset.slotType;

        if (brick && brick.brickType === slotType) {
            // Check if brick is already in this slot
            if (!state.assembler[slotType].includes(brickId)) {
                state.assembler[slotType].push(brickId);
                renderAssembler();
            }
        } else {
            // Visual feedback for incorrect drop
            slot.classList.add('border-red-500');
            setTimeout(() => slot.classList.remove('border-red-500'), 500);
        }
    });

    // Remove from assembler
    assemblerSlots.addEventListener('click', (e) => {
        if (e.target.classList.contains('remove-from-assembler')) {
            const slotType = e.target.dataset.slotType;
            const brickId = e.target.dataset.brickId;
            state.assembler[slotType] = state.assembler[slotType].filter(id => id !== brickId);
            renderAssembler();
        }
    });

    // AI Generation
    generateStoryBtn.addEventListener('click', async () => {
        const finalPrompt = finalPromptPreview.value;
        if (!finalPrompt) {
            aiOutputContent.textContent = "Please add bricks to the assembler to create a prompt.";
            return;
        }

        // Get story configuration
        const storyConfig = {
            length: storyLengthSelect.value,
            audience: targetAudienceSelect.value,
            continuation: continuationCheckbox.checked,
            selectedTitle: state.selectedTitle || null
        };

        aiOutputContent.innerHTML = '<div class="flex items-center justify-center h-full"><div class="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-400"></div><span class="ml-3">Generating...</span></div>';

        try {
            const response = await fetch('api/chat.php', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    prompt: finalPrompt,
                    config: storyConfig
                })
            });
            const result = await response.json();

            if (result.success) {
                aiOutputContent.textContent = result.story;
                copyStoryBtn.classList.remove('hidden');
            } else {
                aiOutputContent.textContent = `Error: ${result.message}`;
                copyStoryBtn.classList.add('hidden');
            }
        } catch (error) {
            aiOutputContent.textContent = 'An error occurred while connecting to the AI service.';
            copyStoryBtn.classList.add('hidden');
            console.error('AI Generation Error:', error);
        }
    });

    // Copy story functionality
    copyStoryBtn.addEventListener('click', async () => {
        try {
            await navigator.clipboard.writeText(aiOutputContent.textContent);

            // Visual feedback
            const originalText = copyStoryBtn.textContent;
            copyStoryBtn.textContent = '✓ Copied!';
            copyStoryBtn.classList.remove('bg-slate-600', 'hover:bg-slate-500');
            copyStoryBtn.classList.add('bg-green-600', 'hover:bg-green-500');

            setTimeout(() => {
                copyStoryBtn.textContent = originalText;
                copyStoryBtn.classList.remove('bg-green-600', 'hover:bg-green-500');
                copyStoryBtn.classList.add('bg-slate-600', 'hover:bg-slate-500');
            }, 2000);
        } catch (error) {
            console.error('Failed to copy story:', error);
            alert('Failed to copy story to clipboard. Please select and copy manually.');
        }
    });

    // Update prompt preview when configuration changes
    storyLengthSelect.addEventListener('change', updatePromptPreview);
    targetAudienceSelect.addEventListener('change', updatePromptPreview);
    continuationCheckbox.addEventListener('change', updatePromptPreview);

    // --- INITIAL RENDER ---
    renderLibraryTabs();
    renderCategoryTabs();
    renderBrickLibrary();
    renderAssembler();
    loadStories();
});
