document.addEventListener('DOMContentLoaded', () => {
    // --- STATE ---
    let state = {
        bricks: initialBricks || [],
        stories: initialStories || [],
        assembler: { Character: [], Setting: [], Plot: [], Theme: [] },
        currentCategory: 'all',
        currentLibrary: 'blocks',
        editingBrick: null,
        editingStory: null
    };

    // --- SELECTORS ---
    const brickLibraryList = document.getElementById('brick-library-list');
    const storyLibraryList = document.getElementById('story-library-list');
    const blockLibraryView = document.getElementById('block-library-view');
    const storyLibraryView = document.getElementById('story-library-view');
    const addNewBrickBtn = document.getElementById('add-new-brick-btn');
    const saveCurrentStoryBtn = document.getElementById('save-current-story-btn');
    const brickTypeDropdown = document.getElementById('brick-type-dropdown');
    const modalOverlay = document.getElementById('modal-overlay');
    const modalTitle = document.getElementById('modal-title');
    const cancelBrickBtn = document.getElementById('cancel-brick-btn');
    const brickForm = document.getElementById('brick-form');
    const brickTypeInput = document.getElementById('brick-type-input');
    const editBrickIdInput = document.getElementById('edit-brick-id');
    const assemblerSlots = document.getElementById('prompt-assembler-slots');
    const finalPromptPreview = document.getElementById('final-prompt-preview');
    const generateStoryBtn = document.getElementById('generate-story-btn');
    const aiOutputContent = document.getElementById('ai-output-content');
    const copyStoryBtn = document.getElementById('copy-story-btn');
    const categoryTabs = document.querySelectorAll('.category-tab');
    const libraryTabs = document.querySelectorAll('.library-tab');

    // Story configuration selectors
    const storyLengthSelect = document.getElementById('story-length-select');
    const targetAudienceSelect = document.getElementById('target-audience-select');
    const continuationCheckbox = document.getElementById('continuation-checkbox');

    // Story modal selectors
    const storyModalOverlay = document.getElementById('story-modal-overlay');
    const storyModalTitle = document.getElementById('story-modal-title');
    const storyForm = document.getElementById('story-form');
    const storyTitleInput = document.getElementById('story-title-input');
    const storyDescriptionInput = document.getElementById('story-description-input');
    const storyBlocksPreview = document.getElementById('story-blocks-preview');
    const editStoryIdInput = document.getElementById('edit-story-id');
    const cancelStoryBtn = document.getElementById('cancel-story-btn');
    const saveStoryBtn = document.getElementById('save-story-btn');


    // --- RENDER FUNCTIONS ---
    const renderBrickLibrary = () => {
        brickLibraryList.innerHTML = '';

        // Filter bricks by current category
        const filteredBricks = state.currentCategory === 'all'
            ? state.bricks
            : state.bricks.filter(brick => brick.brickType === state.currentCategory);

        if (filteredBricks.length === 0) {
            const message = state.currentCategory === 'all'
                ? 'No bricks created yet. Click "+ New Brick" to start!'
                : `No ${state.currentCategory} bricks created yet.`;
            brickLibraryList.innerHTML = `<p class="text-slate-500 text-center">${message}</p>`;
            return;
        }

        filteredBricks.forEach(brick => {
            const card = document.createElement('div');
            card.className = 'bg-slate-700 p-4 rounded-lg hover:bg-slate-600 transition-colors relative group';
            card.setAttribute('draggable', 'true');
            card.dataset.brickId = brick.id;

            // Get type-specific color
            const typeColors = {
                'Character': 'text-blue-400',
                'Setting': 'text-green-400',
                'Plot': 'text-yellow-400',
                'Theme': 'text-purple-400'
            };

            card.innerHTML = `
                <div class="flex justify-between items-start mb-2">
                    <div class="flex-1 min-w-0">
                        <h3 class="font-bold text-lg truncate">${brick.brickName}</h3>
                        <p class="text-sm ${typeColors[brick.brickType] || 'text-indigo-400'}">${brick.brickType}</p>
                    </div>
                    <div class="flex space-x-1 opacity-0 group-hover:opacity-100 transition-opacity ml-2">
                        <button class="edit-brick-btn p-1 hover:bg-slate-500 rounded text-xs" data-brick-id="${brick.id}" title="Edit">✏️</button>
                        <button class="clone-brick-btn p-1 hover:bg-slate-500 rounded text-xs" data-brick-id="${brick.id}" title="Clone">📋</button>
                        <button class="delete-brick-btn p-1 hover:bg-red-600 rounded text-xs" data-brick-id="${brick.id}" title="Delete">🗑️</button>
                    </div>
                </div>
            `;
            brickLibraryList.appendChild(card);
        });
    };
    
    const renderAssembler = () => {
        Object.keys(state.assembler).forEach(slotType => {
            const slot = document.querySelector(`[data-slot-type="${slotType}"]`);
            const brickIds = state.assembler[slotType];

            if (brickIds.length > 0) {
                // Filter out any deleted bricks
                const validBricks = brickIds.map(id => state.bricks.find(b => b.id === id)).filter(Boolean);

                // Update state to remove any deleted bricks
                state.assembler[slotType] = validBricks.map(b => b.id);

                if (validBricks.length > 0) {
                    slot.className = 'min-h-24 bg-indigo-900/50 border-2 border-indigo-500 rounded-lg p-3 space-y-2';
                    slot.innerHTML = validBricks.map(brick => `
                        <div class="flex items-center justify-between bg-indigo-800/50 rounded p-2">
                            <div class="flex flex-col">
                                <h4 class="font-bold text-sm">${brick.brickName}</h4>
                                <p class="text-xs text-indigo-300">${brick.brickType}</p>
                            </div>
                            <button class="remove-from-assembler text-red-400 hover:text-red-300 text-lg" data-slot-type="${slotType}" data-brick-id="${brick.id}" title="Remove">×</button>
                        </div>
                    `).join('');
                } else {
                    slot.className = 'h-24 border-2 border-dashed border-slate-600 rounded-lg flex items-center justify-center text-slate-500';
                    slot.innerHTML = `Drop ${slotType} Brick Here`;
                }
            } else {
                slot.className = 'h-24 border-2 border-dashed border-slate-600 rounded-lg flex items-center justify-center text-slate-500';
                slot.innerHTML = `Drop ${slotType} Brick Here`;
            }
        });
        updatePromptPreview();
    };

    const updatePromptPreview = () => {
        let fullPrompt = '';
        Object.values(state.assembler).forEach(brickIds => {
            if (brickIds.length > 0) {
                brickIds.forEach(brickId => {
                    const brick = state.bricks.find(b => b.id === brickId);
                    if (brick) {
                        fullPrompt += brick.promptText + '\n\n';
                    }
                });
            }
        });
        finalPromptPreview.value = fullPrompt.trim();
    };

    const renderCategoryTabs = () => {
        categoryTabs.forEach(tab => {
            const category = tab.dataset.category;
            if (category === state.currentCategory) {
                tab.classList.add('bg-indigo-600', 'text-white');
                tab.classList.remove('text-slate-400', 'hover:text-white');
            } else {
                tab.classList.remove('bg-indigo-600', 'text-white');
                tab.classList.add('text-slate-400', 'hover:text-white');
            }
        });
    };

    const renderLibraryTabs = () => {
        libraryTabs.forEach(tab => {
            const library = tab.dataset.library;
            if (library === state.currentLibrary) {
                tab.classList.add('bg-indigo-600', 'text-white');
                tab.classList.remove('text-slate-400', 'hover:text-white');
            } else {
                tab.classList.remove('bg-indigo-600', 'text-white');
                tab.classList.add('text-slate-400', 'hover:text-white');
            }
        });

        // Show/hide library views
        if (state.currentLibrary === 'blocks') {
            blockLibraryView.classList.remove('hidden');
            blockLibraryView.classList.add('flex');
            storyLibraryView.classList.add('hidden');
            storyLibraryView.classList.remove('flex');
        } else {
            blockLibraryView.classList.add('hidden');
            blockLibraryView.classList.remove('flex');
            storyLibraryView.classList.remove('hidden');
            storyLibraryView.classList.add('flex');
        }
    };

    const renderStoryLibrary = () => {
        storyLibraryList.innerHTML = '';

        if (state.stories.length === 0) {
            storyLibraryList.innerHTML = `<p class="text-slate-500 text-center">No stories saved yet. Create a story by adding blocks to the assembler and clicking "Save Current".</p>`;
            return;
        }

        // Sort stories by updated_at (most recent first)
        const sortedStories = [...state.stories].sort((a, b) =>
            new Date(b.updated_at) - new Date(a.updated_at)
        );

        sortedStories.forEach(story => {
            const card = document.createElement('div');
            card.className = 'bg-slate-700 p-4 rounded-lg hover:bg-slate-600 transition-colors relative group cursor-pointer';
            card.dataset.storyId = story.id;

            const blockCount = Object.values(story.assembler).reduce((total, blocks) => total + blocks.length, 0);
            const lastUpdated = new Date(story.updated_at).toLocaleDateString();

            card.innerHTML = `
                <div class="flex justify-between items-start mb-2">
                    <div class="flex-1 min-w-0">
                        <h3 class="font-bold text-lg truncate">${story.title}</h3>
                        <p class="text-sm text-slate-400">${blockCount} blocks • Updated ${lastUpdated}</p>
                        ${story.description ? `<p class="text-xs text-slate-500 mt-1 line-clamp-2">${story.description}</p>` : ''}
                    </div>
                    <div class="flex space-x-1 opacity-0 group-hover:opacity-100 transition-opacity ml-2">
                        <button class="edit-story-btn p-1 hover:bg-slate-500 rounded text-xs" data-story-id="${story.id}" title="Edit">✏️</button>
                        <button class="clone-story-btn p-1 hover:bg-slate-500 rounded text-xs" data-story-id="${story.id}" title="Clone">📋</button>
                        <button class="delete-story-btn p-1 hover:bg-red-600 rounded text-xs" data-story-id="${story.id}" title="Delete">🗑️</button>
                    </div>
                </div>
                <div class="text-xs text-slate-400">
                    ${Object.entries(story.assembler).map(([type, blocks]) =>
                        blocks.length > 0 ? `${blocks.length} ${type}${blocks.length > 1 ? 's' : ''}` : ''
                    ).filter(Boolean).join(' • ')}
                </div>
            `;

            storyLibraryList.appendChild(card);
        });
    };

    const showModal = (brickType, editBrick = null) => {
        state.editingBrick = editBrick;
        brickTypeInput.value = brickType;
        editBrickIdInput.value = editBrick ? editBrick.id : '';

        // Update modal title
        modalTitle.textContent = editBrick ? `Edit ${brickType} Brick` : `Create New ${brickType} Brick`;

        // Hide all field groups
        document.querySelectorAll('[id$="-fields"]').forEach(group => group.classList.add('hidden'));

        // Show relevant field group
        const fieldGroup = document.getElementById(`${brickType.toLowerCase()}-fields`);
        if (fieldGroup) {
            fieldGroup.classList.remove('hidden');
        }

        // Populate form if editing
        if (editBrick) {
            document.getElementById('brick-name-input').value = editBrick.brickName;
            populateFormFields(brickType, editBrick.data);
        } else {
            brickForm.reset();
            brickTypeInput.value = brickType;
        }

        modalOverlay.classList.remove('hidden');
    };

    const populateFormFields = (brickType, data) => {
        switch (brickType) {
            case 'Character':
                document.getElementById('character-archetype-input').value = data.archetype || '';
                document.getElementById('character-backstory-input').value = data.backstory || '';
                document.getElementById('character-goal-input').value = data.goal || '';
                break;
            case 'Setting':
                document.getElementById('setting-location-input').value = data.location || '';
                document.getElementById('setting-time-input').value = data.time || '';
                document.getElementById('setting-atmosphere-input').value = data.atmosphere || '';
                break;
            case 'Plot':
                document.getElementById('plot-conflict-input').value = data.conflict || '';
                document.getElementById('plot-inciting-input').value = data.inciting || '';
                document.getElementById('plot-resolution-input').value = data.resolution || '';
                break;
            case 'Theme':
                document.getElementById('theme-message-input').value = data.message || '';
                document.getElementById('theme-tone-input').value = data.tone || '';
                document.getElementById('theme-symbolism-input').value = data.symbolism || '';
                break;
        }
    };

    const showStoryModal = (editStory = null) => {
        state.editingStory = editStory;
        editStoryIdInput.value = editStory ? editStory.id : '';

        // Update modal title
        storyModalTitle.textContent = editStory ? 'Edit Story' : 'Save Story';

        // Populate form if editing
        if (editStory) {
            storyTitleInput.value = editStory.title;
            storyDescriptionInput.value = editStory.description || '';
        } else {
            storyForm.reset();
        }

        // Show current blocks preview
        updateStoryBlocksPreview();

        storyModalOverlay.classList.remove('hidden');
    };

    const updateStoryBlocksPreview = () => {
        const blockCounts = Object.entries(state.assembler).map(([type, blocks]) => {
            if (blocks.length > 0) {
                const blockNames = blocks.map(id => {
                    const brick = state.bricks.find(b => b.id === id);
                    return brick ? brick.brickName : 'Unknown';
                }).join(', ');
                return `<div><strong>${type}:</strong> ${blockNames}</div>`;
            }
            return '';
        }).filter(Boolean);

        if (blockCounts.length === 0) {
            storyBlocksPreview.innerHTML = '<div class="text-slate-500">No blocks in assembler</div>';
        } else {
            storyBlocksPreview.innerHTML = blockCounts.join('');
        }
    };

    const loadStoryIntoAssembler = (story) => {
        // Clear current assembler
        state.assembler = { Character: [], Setting: [], Plot: [], Theme: [] };

        // Load story's assembler state
        Object.keys(story.assembler).forEach(type => {
            if (story.assembler[type]) {
                state.assembler[type] = [...story.assembler[type]];
            }
        });

        // Re-render assembler and switch to blocks view
        renderAssembler();
        state.currentLibrary = 'blocks';
        renderLibraryTabs();
        renderBrickLibrary();
    };


    // --- API & DATA FUNCTIONS ---
    const saveNewBrick = async (newBrick) => {
        try {
            const response = await fetch('api/data.php', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ newBrick })
            });
            const result = await response.json();
            if (result.success) {
                state.bricks = result.bricks;
                renderBrickLibrary();
                renderAssembler();
            } else {
                console.error('Failed to save brick:', result.message);
                alert('Failed to save brick: ' + result.message);
            }
        } catch (error) {
            console.error('Error saving brick:', error);
            alert('Error saving brick. Please try again.');
        }
    };

    const updateBrick = async (brickId, updatedBrick) => {
        try {
            const response = await fetch('api/data.php', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ brickId, updateBrick: updatedBrick })
            });
            const result = await response.json();
            if (result.success) {
                state.bricks = result.bricks;
                renderBrickLibrary();
                renderAssembler();
            } else {
                console.error('Failed to update brick:', result.message);
                alert('Failed to update brick: ' + result.message);
            }
        } catch (error) {
            console.error('Error updating brick:', error);
            alert('Error updating brick. Please try again.');
        }
    };

    const deleteBrick = async (brickId) => {
        if (!confirm('Are you sure you want to delete this brick?')) return;

        try {
            const response = await fetch('api/data.php', {
                method: 'DELETE',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ brickId })
            });
            const result = await response.json();
            if (result.success) {
                state.bricks = result.bricks;
                // Remove from assembler if it was there
                Object.keys(state.assembler).forEach(slotType => {
                    state.assembler[slotType] = state.assembler[slotType].filter(id => id !== brickId);
                });
                renderBrickLibrary();
                renderAssembler();
            } else {
                console.error('Failed to delete brick:', result.message);
                alert('Failed to delete brick: ' + result.message);
            }
        } catch (error) {
            console.error('Error deleting brick:', error);
            alert('Error deleting brick. Please try again.');
        }
    };

    const cloneBrick = async (brickId) => {
        try {
            const response = await fetch('api/data.php', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ cloneBrick: brickId })
            });
            const result = await response.json();
            if (result.success) {
                state.bricks = result.bricks;
                renderBrickLibrary();
            } else {
                console.error('Failed to clone brick:', result.message);
                alert('Failed to clone brick: ' + result.message);
            }
        } catch (error) {
            console.error('Error cloning brick:', error);
            alert('Error cloning brick. Please try again.');
        }
    };

    // === STORY API FUNCTIONS ===

    const loadStories = async () => {
        try {
            const response = await fetch('api/stories.php');
            const result = await response.json();
            if (result.success) {
                state.stories = result.stories;
                renderStoryLibrary();
            } else {
                console.error('Failed to load stories:', result.message);
            }
        } catch (error) {
            console.error('Error loading stories:', error);
        }
    };

    const saveNewStory = async (newStory) => {
        try {
            const response = await fetch('api/stories.php', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ newStory })
            });
            const result = await response.json();
            if (result.success) {
                state.stories = result.stories;
                renderStoryLibrary();
            } else {
                console.error('Failed to save story:', result.message);
                alert('Failed to save story: ' + result.message);
            }
        } catch (error) {
            console.error('Error saving story:', error);
            alert('Error saving story. Please try again.');
        }
    };

    const updateStory = async (storyId, updatedStory) => {
        try {
            const response = await fetch('api/stories.php', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ storyId, updateStory: updatedStory })
            });
            const result = await response.json();
            if (result.success) {
                state.stories = result.stories;
                renderStoryLibrary();
            } else {
                console.error('Failed to update story:', result.message);
                alert('Failed to update story: ' + result.message);
            }
        } catch (error) {
            console.error('Error updating story:', error);
            alert('Error updating story. Please try again.');
        }
    };

    const deleteStory = async (storyId) => {
        if (!confirm('Are you sure you want to delete this story?')) return;

        try {
            const response = await fetch('api/stories.php', {
                method: 'DELETE',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ storyId })
            });
            const result = await response.json();
            if (result.success) {
                state.stories = result.stories;
                renderStoryLibrary();
            } else {
                console.error('Failed to delete story:', result.message);
                alert('Failed to delete story: ' + result.message);
            }
        } catch (error) {
            console.error('Error deleting story:', error);
            alert('Error deleting story. Please try again.');
        }
    };

    const cloneStory = async (storyId) => {
        try {
            const response = await fetch('api/stories.php', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ cloneStory: storyId })
            });
            const result = await response.json();
            if (result.success) {
                state.stories = result.stories;
                renderStoryLibrary();
            } else {
                console.error('Failed to clone story:', result.message);
                alert('Failed to clone story: ' + result.message);
            }
        } catch (error) {
            console.error('Error cloning story:', error);
            alert('Error cloning story. Please try again.');
        }
    };


    // --- EVENT LISTENERS ---
    // Dropdown for new brick types
    addNewBrickBtn.addEventListener('click', (e) => {
        e.stopPropagation();
        brickTypeDropdown.classList.toggle('hidden');
    });

    // Hide dropdown when clicking outside
    document.addEventListener('click', () => {
        brickTypeDropdown.classList.add('hidden');
    });

    // Brick type selection
    brickTypeDropdown.addEventListener('click', (e) => {
        if (e.target.dataset.brickType) {
            const brickType = e.target.dataset.brickType;
            showModal(brickType);
            brickTypeDropdown.classList.add('hidden');
        }
    });

    // Library tabs
    libraryTabs.forEach(tab => {
        tab.addEventListener('click', () => {
            state.currentLibrary = tab.dataset.library;
            renderLibraryTabs();
            if (state.currentLibrary === 'stories') {
                loadStories();
            }
        });
    });

    // Category tabs
    categoryTabs.forEach(tab => {
        tab.addEventListener('click', () => {
            state.currentCategory = tab.dataset.category;
            renderCategoryTabs();
            renderBrickLibrary();
        });
    });

    // Modal controls
    cancelBrickBtn.addEventListener('click', () => {
        modalOverlay.classList.add('hidden');
        state.editingBrick = null;
    });

    modalOverlay.addEventListener('click', (e) => {
        if (e.target === modalOverlay) {
            modalOverlay.classList.add('hidden');
            state.editingBrick = null;
        }
    });

    // Story modal controls
    saveCurrentStoryBtn.addEventListener('click', () => {
        // Check if there are blocks in assembler
        const hasBlocks = Object.values(state.assembler).some(blocks => blocks.length > 0);
        if (!hasBlocks) {
            alert('Please add some blocks to the assembler before saving a story.');
            return;
        }
        showStoryModal();
    });

    cancelStoryBtn.addEventListener('click', () => {
        storyModalOverlay.classList.add('hidden');
        state.editingStory = null;
    });

    storyModalOverlay.addEventListener('click', (e) => {
        if (e.target === storyModalOverlay) {
            storyModalOverlay.classList.add('hidden');
            state.editingStory = null;
        }
    });

    // Form submission
    brickForm.addEventListener('submit', (e) => {
        e.preventDefault();

        const brickType = brickTypeInput.value;
        const brickName = document.getElementById('brick-name-input').value;
        const editBrickId = editBrickIdInput.value;

        let data = {};
        let promptText = '';

        // Collect data based on brick type
        switch (brickType) {
            case 'Character':
                data = {
                    archetype: document.getElementById('character-archetype-input').value,
                    backstory: document.getElementById('character-backstory-input').value,
                    goal: document.getElementById('character-goal-input').value
                };
                promptText = `Character: ${brickName} is a ${data.archetype}. Background: ${data.backstory}. Primary goal: ${data.goal}.`;
                break;

            case 'Setting':
                data = {
                    location: document.getElementById('setting-location-input').value,
                    time: document.getElementById('setting-time-input').value,
                    atmosphere: document.getElementById('setting-atmosphere-input').value
                };
                promptText = `Setting: The story takes place in ${data.location} during ${data.time}. Atmosphere: ${data.atmosphere}.`;
                break;

            case 'Plot':
                data = {
                    conflict: document.getElementById('plot-conflict-input').value,
                    inciting: document.getElementById('plot-inciting-input').value,
                    resolution: document.getElementById('plot-resolution-input').value
                };
                promptText = `Plot: Main conflict - ${data.conflict}. Inciting incident: ${data.inciting}. Resolution direction: ${data.resolution}.`;
                break;

            case 'Theme':
                data = {
                    message: document.getElementById('theme-message-input').value,
                    tone: document.getElementById('theme-tone-input').value,
                    symbolism: document.getElementById('theme-symbolism-input').value
                };
                promptText = `Theme: Central message - ${data.message}. Tone: ${data.tone}. Symbolism: ${data.symbolism}.`;
                break;
        }

        const brickData = {
            brickType,
            brickName,
            data,
            promptText
        };

        if (editBrickId) {
            // Update existing brick
            updateBrick(editBrickId, brickData);
        } else {
            // Create new brick
            saveNewBrick(brickData);
        }

        brickForm.reset();
        modalOverlay.classList.add('hidden');
        state.editingBrick = null;
    });

    // Story form submission
    storyForm.addEventListener('submit', (e) => {
        e.preventDefault();

        const storyTitle = storyTitleInput.value;
        const storyDescription = storyDescriptionInput.value;
        const editStoryId = editStoryIdInput.value;

        const storyData = {
            title: storyTitle,
            description: storyDescription,
            assembler: JSON.parse(JSON.stringify(state.assembler)) // Deep copy
        };

        if (editStoryId) {
            // Update existing story
            updateStory(editStoryId, storyData);
        } else {
            // Create new story
            saveNewStory(storyData);
        }

        storyForm.reset();
        storyModalOverlay.classList.add('hidden');
        state.editingStory = null;
    });

    // Brick action buttons
    brickLibraryList.addEventListener('click', (e) => {
        const brickId = e.target.dataset.brickId;
        if (!brickId) return;

        if (e.target.classList.contains('edit-brick-btn')) {
            e.stopPropagation();
            const brick = state.bricks.find(b => b.id === brickId);
            if (brick) {
                showModal(brick.brickType, brick);
            }
        } else if (e.target.classList.contains('clone-brick-btn')) {
            e.stopPropagation();
            cloneBrick(brickId);
        } else if (e.target.classList.contains('delete-brick-btn')) {
            e.stopPropagation();
            deleteBrick(brickId);
        }
    });

    // Story action buttons
    storyLibraryList.addEventListener('click', (e) => {
        const storyId = e.target.dataset.storyId;
        if (!storyId) {
            // Check if clicked on story card itself (load story)
            const storyCard = e.target.closest('[data-story-id]');
            if (storyCard && !e.target.closest('button')) {
                const clickedStoryId = storyCard.dataset.storyId;
                const story = state.stories.find(s => s.id === clickedStoryId);
                if (story) {
                    loadStoryIntoAssembler(story);
                }
            }
            return;
        }

        if (e.target.classList.contains('edit-story-btn')) {
            e.stopPropagation();
            const story = state.stories.find(s => s.id === storyId);
            if (story) {
                showStoryModal(story);
            }
        } else if (e.target.classList.contains('clone-story-btn')) {
            e.stopPropagation();
            cloneStory(storyId);
        } else if (e.target.classList.contains('delete-story-btn')) {
            e.stopPropagation();
            deleteStory(storyId);
        }
    });

    // Drag and Drop
    brickLibraryList.addEventListener('dragstart', (e) => {
        if (e.target.dataset.brickId) {
            e.dataTransfer.setData('text/plain', e.target.dataset.brickId);
        }
    });

    assemblerSlots.addEventListener('dragover', (e) => {
        e.preventDefault();
    });

    assemblerSlots.addEventListener('drop', (e) => {
        e.preventDefault();
        const slot = e.target.closest('[data-slot-type]');
        if (!slot) return;

        const brickId = e.dataTransfer.getData('text/plain');
        const brick = state.bricks.find(b => b.id === brickId);
        const slotType = slot.dataset.slotType;

        if (brick && brick.brickType === slotType) {
            // Check if brick is already in this slot
            if (!state.assembler[slotType].includes(brickId)) {
                state.assembler[slotType].push(brickId);
                renderAssembler();
            }
        } else {
            // Visual feedback for incorrect drop
            slot.classList.add('border-red-500');
            setTimeout(() => slot.classList.remove('border-red-500'), 500);
        }
    });

    // Remove from assembler
    assemblerSlots.addEventListener('click', (e) => {
        if (e.target.classList.contains('remove-from-assembler')) {
            const slotType = e.target.dataset.slotType;
            const brickId = e.target.dataset.brickId;
            state.assembler[slotType] = state.assembler[slotType].filter(id => id !== brickId);
            renderAssembler();
        }
    });

    // AI Generation
    generateStoryBtn.addEventListener('click', async () => {
        const finalPrompt = finalPromptPreview.value;
        if (!finalPrompt) {
            aiOutputContent.textContent = "Please add bricks to the assembler to create a prompt.";
            return;
        }

        aiOutputContent.innerHTML = '<div class="flex items-center justify-center h-full"><div class="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-400"></div><span class="ml-3">Generating...</span></div>';

        try {
            const response = await fetch('api/chat.php', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ prompt: finalPrompt })
            });
            const result = await response.json();

            if (result.success) {
                aiOutputContent.textContent = result.story;
            } else {
                aiOutputContent.textContent = `Error: ${result.message}`;
            }
        } catch (error) {
            aiOutputContent.textContent = 'An error occurred while connecting to the AI service.';
            console.error('AI Generation Error:', error);
        }
    });

    // --- INITIAL RENDER ---
    renderLibraryTabs();
    renderCategoryTabs();
    renderBrickLibrary();
    renderAssembler();
    loadStories();
});
