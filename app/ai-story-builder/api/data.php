<?php
header('Content-Type: application/json');
require_once '../includes/DataStorage.php';

session_start();
$userId = $_SESSION['user_id'] ?? 'default_user';
$dataStorage = new DataStorage($userId);

$method = $_SERVER['REQUEST_METHOD'];

if ($method === 'GET') {
    $bricks = $dataStorage->loadBricks();
    echo json_encode(['success' => true, 'bricks' => $bricks]);
} elseif ($method === 'POST') {
    $input = json_decode(file_get_contents('php://input'), true);

    if (isset($input['newBrick'])) {
        $bricks = $dataStorage->loadBricks();
        $newBrick = $input['newBrick'];
        // Assign a unique ID to the new brick
        $newBrick['id'] = uniqid('brick_');
        $bricks[] = $newBrick;

        if ($dataStorage->saveBricks($bricks)) {
            echo json_encode(['success' => true, 'bricks' => $bricks]);
        } else {
            echo json_encode(['success' => false, 'message' => 'Failed to save brick.']);
        }
    } else {
        echo json_encode(['success' => false, 'message' => 'Invalid data.']);
    }
} else {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed.']);
}
?>
