<?php
header('Content-Type: application/json');
require_once '../includes/DataStorage.php';

session_start();
$userId = $_SESSION['user_id'] ?? 'default_user';
$dataStorage = new DataStorage($userId);

$method = $_SERVER['REQUEST_METHOD'];

if ($method === 'GET') {
    $bricks = $dataStorage->loadBricks();
    echo json_encode(['success' => true, 'bricks' => $bricks]);
} elseif ($method === 'POST') {
    $input = json_decode(file_get_contents('php://input'), true);

    if (isset($input['newBrick'])) {
        // Create new brick
        $bricks = $dataStorage->loadBricks();
        $newBrick = $input['newBrick'];
        $newBrick['id'] = uniqid('brick_');
        $bricks[] = $newBrick;

        if ($dataStorage->saveBricks($bricks)) {
            echo json_encode(['success' => true, 'bricks' => $bricks]);
        } else {
            echo json_encode(['success' => false, 'message' => 'Failed to save brick.']);
        }
    } elseif (isset($input['updateBrick'])) {
        // Update existing brick
        $brickId = $input['brickId'];
        $updatedBrick = $input['updateBrick'];

        if ($dataStorage->updateBrick($brickId, $updatedBrick)) {
            $bricks = $dataStorage->loadBricks();
            echo json_encode(['success' => true, 'bricks' => $bricks]);
        } else {
            echo json_encode(['success' => false, 'message' => 'Failed to update brick.']);
        }
    } elseif (isset($input['cloneBrick'])) {
        // Clone existing brick
        $brickId = $input['cloneBrick'];
        $clonedBrick = $dataStorage->cloneBrick($brickId);

        if ($clonedBrick) {
            $bricks = $dataStorage->loadBricks();
            echo json_encode(['success' => true, 'bricks' => $bricks, 'cloned' => $clonedBrick]);
        } else {
            echo json_encode(['success' => false, 'message' => 'Failed to clone brick.']);
        }
    } else {
        echo json_encode(['success' => false, 'message' => 'Invalid data.']);
    }
} elseif ($method === 'DELETE') {
    $input = json_decode(file_get_contents('php://input'), true);
    $brickId = $input['brickId'] ?? '';

    if (empty($brickId)) {
        echo json_encode(['success' => false, 'message' => 'Brick ID is required.']);
    } else {
        if ($dataStorage->deleteBrick($brickId)) {
            $bricks = $dataStorage->loadBricks();
            echo json_encode(['success' => true, 'bricks' => $bricks]);
        } else {
            echo json_encode(['success' => false, 'message' => 'Failed to delete brick.']);
        }
    }
} else {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed.']);
}
?>
