<?php
header('Content-Type: application/json');
require_once '../includes/DataStorage.php';

session_start();
$userId = $_SESSION['user_id'] ?? 'default_user';
$dataStorage = new DataStorage($userId);

$method = $_SERVER['REQUEST_METHOD'];

if ($method === 'GET') {
    $stories = $dataStorage->loadStories();
    echo json_encode(['success' => true, 'stories' => $stories]);
} elseif ($method === 'POST') {
    $input = json_decode(file_get_contents('php://input'), true);

    if (isset($input['newStory'])) {
        // Create new story
        $newStory = $input['newStory'];
        $savedStory = $dataStorage->saveStory($newStory);
        
        if ($savedStory) {
            $stories = $dataStorage->loadStories();
            echo json_encode(['success' => true, 'stories' => $stories, 'story' => $savedStory]);
        } else {
            echo json_encode(['success' => false, 'message' => 'Failed to save story.']);
        }
    } elseif (isset($input['updateStory'])) {
        // Update existing story
        $storyId = $input['storyId'];
        $updatedStory = $input['updateStory'];
        
        if ($dataStorage->updateStory($storyId, $updatedStory)) {
            $stories = $dataStorage->loadStories();
            echo json_encode(['success' => true, 'stories' => $stories]);
        } else {
            echo json_encode(['success' => false, 'message' => 'Failed to update story.']);
        }
    } elseif (isset($input['cloneStory'])) {
        // Clone existing story
        $storyId = $input['cloneStory'];
        $clonedStory = $dataStorage->cloneStory($storyId);
        
        if ($clonedStory) {
            $stories = $dataStorage->loadStories();
            echo json_encode(['success' => true, 'stories' => $stories, 'cloned' => $clonedStory]);
        } else {
            echo json_encode(['success' => false, 'message' => 'Failed to clone story.']);
        }
    } else {
        echo json_encode(['success' => false, 'message' => 'Invalid data.']);
    }
} elseif ($method === 'DELETE') {
    $input = json_decode(file_get_contents('php://input'), true);
    $storyId = $input['storyId'] ?? '';
    
    if (empty($storyId)) {
        echo json_encode(['success' => false, 'message' => 'Story ID is required.']);
    } else {
        if ($dataStorage->deleteStory($storyId)) {
            $stories = $dataStorage->loadStories();
            echo json_encode(['success' => true, 'stories' => $stories]);
        } else {
            echo json_encode(['success' => false, 'message' => 'Failed to delete story.']);
        }
    }
} else {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed.']);
}
?>
