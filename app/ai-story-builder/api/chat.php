<?php
header('Content-Type: application/json');

// Include dashboard AI integration
require_once '../../includes/ai_config.php';

session_start();

$input = json_decode(file_get_contents('php://input'), true);
$prompt = $input['prompt'] ?? '';

if (empty($prompt)) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Prompt is empty.']);
    exit;
}

try {
    // Get current user's AI settings
    $userId = $_SESSION['user_id'] ?? null;
    if (!$userId) {
        echo json_encode(['success' => false, 'message' => 'User not logged in.']);
        exit;
    }

    $aiSettings = getUserAISettings($userId);
    if (!$aiSettings['active_model']) {
        echo json_encode(['success' => false, 'message' => 'Please configure your AI settings in the dashboard first.']);
        exit;
    }

    // Get provider and API key
    $provider = getModelProvider($aiSettings['active_model']);
    $apiKey = '';

    switch ($provider) {
        case 'anthropic':
            $apiKey = $aiSettings['anthropic_api_key'];
            break;
        case 'google':
            $apiKey = $aiSettings['google_api_key'];
            break;
        case 'openai':
            $apiKey = $aiSettings['openai_api_key'];
            break;
    }

    if (empty($apiKey)) {
        echo json_encode(['success' => false, 'message' => 'API key not configured for ' . $provider . '.']);
        exit;
    }

    // Enhanced system prompt for story generation
    $systemPrompt = "You are a creative writing assistant specialized in generating engaging stories. Based on the provided character, setting, plot, and theme elements, create a compelling narrative that weaves these elements together naturally. Focus on vivid descriptions, character development, and maintaining narrative flow. Write a complete short story of 500-1000 words.";

    // Send request based on provider
    $result = sendAIRequest($provider, $aiSettings['active_model'], $apiKey, $prompt, $systemPrompt);

    if ($result['success']) {
        echo json_encode(['success' => true, 'story' => $result['response']]);
    } else {
        echo json_encode(['success' => false, 'message' => 'Failed to generate story: ' . ($result['error'] ?? 'Unknown error')]);
    }
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => 'AI Error: ' . $e->getMessage()]);
}

function sendAIRequest($provider, $model, $apiKey, $prompt, $systemPrompt) {
    switch ($provider) {
        case 'openai':
            return sendOpenAIRequest($model, $apiKey, $prompt, $systemPrompt);
        case 'anthropic':
            return sendAnthropicRequest($model, $apiKey, $prompt, $systemPrompt);
        case 'google':
            return sendGoogleRequest($model, $apiKey, $prompt, $systemPrompt);
        default:
            throw new Exception('Unsupported AI provider: ' . $provider);
    }
}

function sendOpenAIRequest($model, $apiKey, $prompt, $systemPrompt) {
    $messages = [
        ['role' => 'system', 'content' => $systemPrompt],
        ['role' => 'user', 'content' => $prompt]
    ];

    $data = [
        'model' => $model,
        'messages' => $messages,
        'max_tokens' => 4000,
        'temperature' => 0.8
    ];

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, 'https://api.openai.com/v1/chat/completions');
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    curl_setopt($ch, CURLOPT_TIMEOUT, 120);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'Authorization: Bearer ' . $apiKey
    ]);

    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    if ($httpCode !== 200) {
        return ['success' => false, 'error' => 'OpenAI API request failed with code: ' . $httpCode];
    }

    $result = json_decode($response, true);

    if (isset($result['error'])) {
        return ['success' => false, 'error' => 'OpenAI API error: ' . $result['error']['message']];
    }

    return [
        'success' => true,
        'response' => $result['choices'][0]['message']['content'] ?? ''
    ];
}

function sendAnthropicRequest($model, $apiKey, $prompt, $systemPrompt) {
    $data = [
        'model' => $model,
        'max_tokens' => 4000,
        'messages' => [
            ['role' => 'user', 'content' => $prompt]
        ],
        'system' => $systemPrompt
    ];

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, 'https://api.anthropic.com/v1/messages');
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    curl_setopt($ch, CURLOPT_TIMEOUT, 120);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'x-api-key: ' . $apiKey,
        'anthropic-version: 2023-06-01'
    ]);

    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    if ($httpCode !== 200) {
        return ['success' => false, 'error' => 'Anthropic API request failed with code: ' . $httpCode];
    }

    $result = json_decode($response, true);

    if (isset($result['error'])) {
        return ['success' => false, 'error' => 'Anthropic API error: ' . $result['error']['message']];
    }

    return [
        'success' => true,
        'response' => $result['content'][0]['text'] ?? ''
    ];
}

function sendGoogleRequest($model, $apiKey, $prompt, $systemPrompt) {
    $fullPrompt = $systemPrompt . "\n\n" . $prompt;

    $data = [
        'contents' => [
            [
                'parts' => [
                    ['text' => $fullPrompt]
                ]
            ]
        ],
        'generationConfig' => [
            'maxOutputTokens' => 4000,
            'temperature' => 0.8
        ]
    ];

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, 'https://generativelanguage.googleapis.com/v1beta/models/' . $model . ':generateContent?key=' . $apiKey);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    curl_setopt($ch, CURLOPT_TIMEOUT, 120);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json'
    ]);

    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    if ($httpCode !== 200) {
        return ['success' => false, 'error' => 'Google API request failed with code: ' . $httpCode];
    }

    $result = json_decode($response, true);

    if (isset($result['error'])) {
        return ['success' => false, 'error' => 'Google API error: ' . $result['error']['message']];
    }

    return [
        'success' => true,
        'response' => $result['candidates'][0]['content']['parts'][0]['text'] ?? ''
    ];
}
?>
