<?php
header('Content-Type: application/json');

// Include dashboard AI integration
require_once '../../includes/ai_client.php';
require_once '../../includes/ai_config.php';

session_start();

$input = json_decode(file_get_contents('php://input'), true);
$prompt = $input['prompt'] ?? '';

if (empty($prompt)) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Prompt is empty.']);
    exit;
}

try {
    // Check if user has AI configured
    if (!currentUserHasAI()) {
        echo json_encode(['success' => false, 'message' => 'Please configure your AI settings in the dashboard first.']);
        exit;
    }

    // Create AI client using dashboard's system
    $client = createAIClient();

    // Enhanced system prompt for story generation
    $systemPrompt = "You are a creative writing assistant specialized in generating engaging stories. Based on the provided character, setting, plot, and theme elements, create a compelling narrative that weaves these elements together naturally. Focus on vivid descriptions, character development, and maintaining narrative flow.";

    // Send request to AI
    $result = $client->sendMessage($prompt, $systemPrompt, [
        'max_tokens' => 4000,
        'temperature' => 0.8
    ]);

    if ($result['success']) {
        echo json_encode(['success' => true, 'story' => $result['content']]);
    } else {
        echo json_encode(['success' => false, 'message' => 'Failed to generate story: ' . ($result['error'] ?? 'Unknown error')]);
    }
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => 'AI Error: ' . $e->getMessage()]);
}
?>
