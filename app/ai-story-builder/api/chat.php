<?php
header('Content-Type: application/json');

// This assumes the dashboard provides an 'AIIntegration' class or similar
// that implements the AppAIInterface. We will simulate this for now.
// In a real environment, you would include the dashboard's integration file.
// require_once '../../core/includes/ai-interface.php';

// --- <PERSON><PERSON><PERSON> AI INTERFACE FOR DEVELOPMENT ---
// In the actual dashboard, this would be provided.
interface AppAIInterface {
    public function sendAIRequest($message, $context = []);
}

class MockAIIntegration implements AppAIInterface {
    public function sendAIRequest($message, $context = []) {
        // This is a mock response. A real implementation would make an API call.
        $storyIntro = "The story begins...\n\n";
        // Simulate a delay
        sleep(2);
        return [
            'success' => true,
            'response' => $storyIntro . $message
        ];
    }
}
// --- END MOCK ---

$input = json_decode(file_get_contents('php://input'), true);
$prompt = $input['prompt'] ?? '';

if (empty($prompt)) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Prompt is empty.']);
    exit;
}

// In a real environment, you would get this from the dashboard's DI container or factory
$aiIntegration = new MockAIIntegration();
$result = $aiIntegration->sendAIRequest($prompt);

if ($result['success']) {
    echo json_encode(['success' => true, 'story' => $result['response']]);
} else {
    echo json_encode(['success' => false, 'message' => 'Failed to get response from AI.']);
}
?>
