<?php
/**
 * AI Story Builder - AI Chat API Endpoint
 *
 * This endpoint handles AI story generation requests using the dashboard's AI system.
 */

// Increase execution time for AI requests
set_time_limit(120); // 2 minutes
ini_set('max_execution_time', 120);

// Include dashboard authentication and configuration
require_once __DIR__ . '/../../../auth.php';
require_once __DIR__ . '/../../../config.php';

// Require authentication
requireAuth();

// Set JSON response header
header('Content-Type: application/json');

// Include our AI integration class
require_once __DIR__ . '/../includes/StoryBuilderAI.php';

// Get request data
$input = json_decode(file_get_contents('php://input'), true);
$prompt = $input['prompt'] ?? '';
$storyConfig = $input['config'] ?? [];

if (empty($prompt)) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Prompt is empty.']);
    exit;
}

try {
    // Create AI instance
    $ai = new StoryBuilderAI();

    // Check if AI is available
    if (!$ai->isAvailable()) {
        $info = $ai->getInfo();
        echo json_encode([
            'success' => false,
            'message' => $info['message']
        ]);
        exit;
    }

    // Generate story with configuration
    $result = $ai->generateStory($prompt, $storyConfig);

    if ($result['success']) {
        echo json_encode([
            'success' => true,
            'story' => $result['story'],
            'model' => $result['model'] ?? 'unknown',
            'usage' => $result['usage'] ?? []
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'message' => $result['error']
        ]);
    }

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'AI Error: ' . $e->getMessage()
    ]);
}

?>
