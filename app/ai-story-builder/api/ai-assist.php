<?php
/**
 * AI Story Builder - AI Assistance API Endpoint
 * 
 * This endpoint handles various AI assistance requests for creative writing.
 */

// Increase execution time for AI requests
set_time_limit(120);
ini_set('max_execution_time', 120);

// Include dashboard authentication and configuration
require_once __DIR__ . '/../../../auth.php';
require_once __DIR__ . '/../../../config.php';

// Require authentication
requireAuth();

// Set JSON response header
header('Content-Type: application/json');

// Include our AI integration class
require_once __DIR__ . '/../includes/StoryBuilderAI.php';
require_once __DIR__ . '/../includes/DataStorage.php';

// Get request data
$input = json_decode(file_get_contents('php://input'), true);
$action = $input['action'] ?? '';
$data = $input['data'] ?? [];

if (empty($action)) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Action is required.']);
    exit;
}

try {
    // Create AI instance
    $ai = new StoryBuilderAI();
    
    // Check if AI is available
    if (!$ai->isAvailable()) {
        $info = $ai->getInfo();
        echo json_encode([
            'success' => false,
            'message' => $info['message']
        ]);
        exit;
    }
    
    // Handle different AI assistance actions
    switch ($action) {
        case 'generate_block':
            $result = generateBlockWithAI($ai, $data);
            break;
            
        case 'deepen_character':
            $result = deepenCharacter($ai, $data);
            break;
            
        case 'generate_dialogue':
            $result = generateDialogue($ai, $data);
            break;
            
        case 'suggest_story':
            $result = suggestStory($ai, $data);
            break;
            
        case 'brainstorm_titles':
            $result = brainstormTitles($ai, $data);
            break;
            
        default:
            $result = ['success' => false, 'message' => 'Unknown action: ' . $action];
    }
    
    echo json_encode($result);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'AI Error: ' . $e->getMessage()
    ]);
}

/**
 * Generate detailed block description from title
 */
function generateBlockWithAI($ai, $data) {
    $blockType = $data['blockType'] ?? '';
    $blockName = $data['blockName'] ?? '';
    
    if (empty($blockType) || empty($blockName)) {
        return ['success' => false, 'message' => 'Block type and name are required.'];
    }
    
    $prompts = [
        'Character' => "Create a detailed character profile for a character named '{$blockName}'. Include their archetype, backstory, and primary goal. Be specific and creative.",
        'Setting' => "Create a detailed setting description for '{$blockName}'. Include the location, time period, and atmosphere/mood. Be vivid and immersive.",
        'Plot' => "Create a detailed plot outline for '{$blockName}'. Include the main conflict, inciting incident, and resolution direction. Be engaging and dramatic.",
        'Theme' => "Create a detailed theme exploration for '{$blockName}'. Include the central message, tone, and symbolism/motifs. Be thoughtful and meaningful."
    ];
    
    $prompt = $prompts[$blockType] ?? "Create a detailed description for '{$blockName}' in the context of {$blockType}.";
    
    $systemPrompt = "You are a creative writing assistant. Generate detailed, creative content for story building blocks. Be specific, vivid, and inspiring.";
    
    $result = $ai->generateStory($prompt, ['length' => 'flash-fiction', 'audience' => 'adult']);
    
    if ($result['success']) {
        return [
            'success' => true,
            'content' => $result['story'],
            'blockType' => $blockType
        ];
    } else {
        return ['success' => false, 'message' => $result['error']];
    }
}

/**
 * Deepen character with secret and core motivation
 */
function deepenCharacter($ai, $data) {
    $characterData = $data['character'] ?? [];
    $name = $characterData['name'] ?? 'the character';
    $archetype = $characterData['archetype'] ?? '';
    $backstory = $characterData['backstory'] ?? '';
    $goal = $characterData['goal'] ?? '';
    
    $prompt = "Based on this character profile:\n";
    $prompt .= "Name: {$name}\n";
    $prompt .= "Archetype: {$archetype}\n";
    $prompt .= "Backstory: {$backstory}\n";
    $prompt .= "Goal: {$goal}\n\n";
    $prompt .= "Add depth by creating:\n1. A secret they're hiding\n2. Their core motivation (what truly drives them)\n\nFormat as: SECRET: [secret] MOTIVATION: [motivation]";
    
    $systemPrompt = "You are a character development expert. Create psychological depth for characters by adding secrets and core motivations that feel authentic and compelling.";
    
    $result = $ai->generateStory($prompt, ['length' => 'flash-fiction', 'audience' => 'adult']);
    
    if ($result['success']) {
        return [
            'success' => true,
            'content' => $result['story']
        ];
    } else {
        return ['success' => false, 'message' => $result['error']];
    }
}

/**
 * Generate sample dialogue for character
 */
function generateDialogue($ai, $data) {
    $characterData = $data['character'] ?? [];
    $name = $characterData['name'] ?? 'the character';
    $archetype = $characterData['archetype'] ?? '';
    $backstory = $characterData['backstory'] ?? '';
    $goal = $characterData['goal'] ?? '';
    
    $prompt = "Based on this character profile:\n";
    $prompt .= "Name: {$name}\n";
    $prompt .= "Archetype: {$archetype}\n";
    $prompt .= "Backstory: {$backstory}\n";
    $prompt .= "Goal: {$goal}\n\n";
    $prompt .= "Generate 3 sample lines of dialogue that this character might say. Each line should reflect their personality, background, and speaking style. Format as numbered lines.";
    
    $systemPrompt = "You are a dialogue expert. Create authentic, character-specific dialogue that reveals personality, background, and motivations through speech patterns and word choices.";
    
    $result = $ai->generateStory($prompt, ['length' => 'flash-fiction', 'audience' => 'adult']);
    
    if ($result['success']) {
        return [
            'success' => true,
            'content' => $result['story']
        ];
    } else {
        return ['success' => false, 'message' => $result['error']];
    }
}

/**
 * Suggest a complete story recipe
 */
function suggestStory($ai, $data) {
    $bricks = $data['bricks'] ?? [];
    
    if (empty($bricks)) {
        return ['success' => false, 'message' => 'No bricks available for story suggestion.'];
    }
    
    // Organize bricks by type
    $characters = array_filter($bricks, fn($b) => $b['brickType'] === 'Character');
    $settings = array_filter($bricks, fn($b) => $b['brickType'] === 'Setting');
    $plots = array_filter($bricks, fn($b) => $b['brickType'] === 'Plot');
    $themes = array_filter($bricks, fn($b) => $b['brickType'] === 'Theme');
    
    $prompt = "Based on this library of story elements:\n\n";
    
    if (!empty($characters)) {
        $prompt .= "CHARACTERS:\n";
        foreach ($characters as $char) {
            $prompt .= "- {$char['brickName']}: {$char['promptText']}\n";
        }
        $prompt .= "\n";
    }
    
    if (!empty($settings)) {
        $prompt .= "SETTINGS:\n";
        foreach ($settings as $setting) {
            $prompt .= "- {$setting['brickName']}: {$setting['promptText']}\n";
        }
        $prompt .= "\n";
    }
    
    if (!empty($plots)) {
        $prompt .= "PLOTS:\n";
        foreach ($plots as $plot) {
            $prompt .= "- {$plot['brickName']}: {$plot['promptText']}\n";
        }
        $prompt .= "\n";
    }
    
    $prompt .= "Suggest a complete story by selecting one character, one setting, and one plot that would work well together. ";
    $prompt .= "Respond in this exact JSON format:\n";
    $prompt .= '{"title": "Story Title", "character": "Character Name", "setting": "Setting Name", "plot": "Plot Name", "reason": "Brief explanation of why these elements work together"}';
    
    $systemPrompt = "You are a story consultant. Analyze story elements and suggest compelling combinations. Always respond with valid JSON in the exact format requested.";
    
    $result = $ai->generateStory($prompt, ['length' => 'flash-fiction', 'audience' => 'adult']);
    
    if ($result['success']) {
        // Parse JSON from response, handling markdown code blocks
        $content = $result['story'];
        $jsonMatch = [];
        
        // Try to extract JSON from markdown code blocks
        if (preg_match('/```(?:json)?\s*(\{.*?\})\s*```/s', $content, $jsonMatch)) {
            $jsonStr = $jsonMatch[1];
        } else {
            // Try to find JSON without code blocks
            if (preg_match('/(\{.*?\})/s', $content, $jsonMatch)) {
                $jsonStr = $jsonMatch[1];
            } else {
                $jsonStr = $content;
            }
        }
        
        $suggestion = json_decode($jsonStr, true);
        
        if ($suggestion) {
            return [
                'success' => true,
                'suggestion' => $suggestion
            ];
        } else {
            return [
                'success' => false,
                'message' => 'Failed to parse AI suggestion. Raw response: ' . $content
            ];
        }
    } else {
        return ['success' => false, 'message' => $result['error']];
    }
}

/**
 * Brainstorm story titles
 */
function brainstormTitles($ai, $data) {
    $assembler = $data['assembler'] ?? [];
    $bricks = $data['bricks'] ?? [];
    
    $prompt = "Based on these selected story elements:\n\n";
    
    foreach ($assembler as $type => $brickIds) {
        if (!empty($brickIds)) {
            $prompt .= "{$type}s:\n";
            foreach ($brickIds as $brickId) {
                $brick = array_filter($bricks, fn($b) => $b['id'] === $brickId);
                if (!empty($brick)) {
                    $brick = array_values($brick)[0];
                    $prompt .= "- {$brick['brickName']}: {$brick['promptText']}\n";
                }
            }
            $prompt .= "\n";
        }
    }
    
    $prompt .= "Generate 5 compelling story titles that capture the essence of these elements. Make them creative, intriguing, and marketable. Format as a numbered list.";
    
    $systemPrompt = "You are a title expert. Create compelling, memorable story titles that capture the essence of the story elements and would attract readers.";
    
    $result = $ai->generateStory($prompt, ['length' => 'flash-fiction', 'audience' => 'adult']);
    
    if ($result['success']) {
        return [
            'success' => true,
            'titles' => $result['story']
        ];
    } else {
        return ['success' => false, 'message' => $result['error']];
    }
}
?>
