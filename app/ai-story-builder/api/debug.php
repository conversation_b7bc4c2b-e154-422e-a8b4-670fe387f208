<?php
header('Content-Type: application/json');
session_start();

// Debug information
$debug = [
    'success' => true,
    'message' => 'Debug endpoint',
    'session_id' => session_id(),
    'user_id' => $_SESSION['user_id'] ?? 'not set',
    'config_exists' => file_exists('../../config.php'),
    'ai_config_exists' => file_exists('../../includes/ai_config.php'),
    'functions_available' => [
        'getUserAISettings' => function_exists('getUserAISettings'),
        'getModelProvider' => function_exists('getModelProvider'),
        'loadUsers' => function_exists('loadUsers')
    ],
    'post_data' => json_decode(file_get_contents('php://input'), true),
    'php_version' => phpversion(),
    'curl_available' => function_exists('curl_init')
];

// Try to include config and see what happens
try {
    if (file_exists('../../config.php')) {
        require_once '../../config.php';
        $debug['config_loaded'] = true;
    }
    if (file_exists('../../includes/ai_config.php')) {
        require_once '../../includes/ai_config.php';
        $debug['ai_config_loaded'] = true;
        
        // Check functions again after include
        $debug['functions_after_include'] = [
            'getUserAISettings' => function_exists('getUserAISettings'),
            'getModelProvider' => function_exists('getModelProvider'),
            'loadUsers' => function_exists('loadUsers')
        ];
        
        // Try to get user settings if possible
        if (function_exists('getUserAISettings') && isset($_SESSION['user_id'])) {
            $debug['user_ai_settings'] = getUserAISettings($_SESSION['user_id']);
        }
    }
} catch (Exception $e) {
    $debug['include_error'] = $e->getMessage();
}

echo json_encode($debug, JSON_PRETTY_PRINT);
?>
