<?php
/**
 * AI Story Builder - AI Integration
 * 
 * This class handles AI integration for story generation using the dashboard's AI system.
 */

require_once __DIR__ . '/../../../includes/ai_client.php';

class StoryBuilderAI {
    private $aiClient;
    
    public function __construct() {
        // Create AI client using dashboard's system
        if (function_exists('createAIClient')) {
            $this->aiClient = createAIClient();
        } else {
            throw new Exception('Dashboard AI client not available');
        }
    }
    
    /**
     * Generate a story from the provided prompt elements
     */
    public function generateStory($prompt, $storyConfig = []) {
        if (!$this->aiClient) {
            throw new Exception('AI client not initialized');
        }

        // Parse story configuration
        $length = $storyConfig['length'] ?? 'short-story';
        $audience = $storyConfig['audience'] ?? 'adult';
        $isContinuation = $storyConfig['continuation'] ?? false;
        $selectedTitle = $storyConfig['selectedTitle'] ?? null;

        // Build enhanced system prompt based on configuration
        $systemPrompt = $this->buildSystemPrompt($length, $audience, $isContinuation, $selectedTitle);

        // Determine token count based on story length
        $maxTokens = $this->getMaxTokensForLength($length);

        try {
            $result = $this->aiClient->sendMessage($prompt, $systemPrompt, [
                'max_tokens' => $maxTokens,
                'temperature' => 0.8
            ]);

            if ($result['success']) {
                return [
                    'success' => true,
                    'story' => $result['content'],
                    'model' => $result['model'] ?? 'unknown',
                    'usage' => $result['usage'] ?? [],
                    'config' => $storyConfig
                ];
            } else {
                return [
                    'success' => false,
                    'error' => 'AI generation failed: ' . ($result['error'] ?? 'Unknown error')
                ];
            }
        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => 'AI Error: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Build system prompt based on story configuration
     */
    private function buildSystemPrompt($length, $audience, $isContinuation, $selectedTitle = null) {
        $basePrompt = "You are a creative writing assistant specialized in generating engaging stories. Based on the provided story elements and configuration, create a compelling narrative that weaves these elements together naturally.";

        // Length-specific instructions
        $lengthInstructions = [
            'flash-fiction' => "Write a complete flash fiction piece (100-1000 words). Focus on a single moment, emotion, or revelation. Every word should count.",
            'short-story' => "Write a complete short story (1000-7500 words). Develop characters, build tension, and provide a satisfying resolution.",
            'novelette' => "Write a substantial novelette section (7500-17500 words). Develop complex characters, multiple plot threads, and rich world-building.",
            'novel-chapter' => "Write a complete novel chapter (2000-5000 words). Focus on advancing the plot, developing characters, and maintaining narrative momentum."
        ];

        // Audience-specific instructions
        $audienceInstructions = [
            'children' => "Write for children ages 5-12. Use simple language, positive themes, and age-appropriate content. Focus on wonder, friendship, and learning.",
            'young-adult' => "Write for young adults ages 13-17. Address coming-of-age themes, identity, relationships, and challenges relevant to teenagers.",
            'adult' => "Write for adult readers. Use sophisticated language and explore complex themes, relationships, and moral dilemmas."
        ];

        // Continuation instructions
        $continuationInstruction = $isContinuation ?
            "This is a continuation of an existing story. Begin where the previous section left off, maintaining consistency with established characters, plot, and tone." :
            "This is a standalone story. Provide a complete narrative arc with clear beginning, middle, and end.";

        // Title instruction
        $titleInstruction = $selectedTitle ?
            "Use the title '{$selectedTitle}' for this story. The story should reflect and justify this title through its content, themes, and narrative arc." :
            "Create an appropriate title for the story and include it at the beginning.";

        return $basePrompt . "\n\n" .
               "LENGTH: " . ($lengthInstructions[$length] ?? $lengthInstructions['short-story']) . "\n\n" .
               "AUDIENCE: " . ($audienceInstructions[$audience] ?? $audienceInstructions['adult']) . "\n\n" .
               "STRUCTURE: " . $continuationInstruction . "\n\n" .
               "TITLE: " . $titleInstruction . "\n\n" .
               "Focus on vivid descriptions, character development, dialogue, and maintaining narrative flow throughout.";
    }

    /**
     * Get maximum tokens based on story length
     */
    private function getMaxTokensForLength($length) {
        $tokenLimits = [
            'flash-fiction' => 2000,   // ~1000 words
            'short-story' => 8000,     // ~4000 words
            'novelette' => 16000,      // ~8000 words
            'novel-chapter' => 6000    // ~3000 words
        ];

        return $tokenLimits[$length] ?? 8000;
    }
    
    /**
     * Check if AI is available and configured
     */
    public function isAvailable() {
        try {
            return $this->aiClient && function_exists('currentUserHasAI') && currentUserHasAI();
        } catch (Exception $e) {
            return false;
        }
    }
    
    /**
     * Get AI configuration info
     */
    public function getInfo() {
        if (!$this->isAvailable()) {
            return [
                'available' => false,
                'message' => 'AI not configured. Please configure your AI settings in the dashboard.'
            ];
        }
        
        return [
            'available' => true,
            'message' => 'AI is configured and ready to generate stories.'
        ];
    }
}
?>
