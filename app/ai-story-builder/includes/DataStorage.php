<?php
class DataStorage {
    private $dataDir;

    public function __construct($userId) {
        // Each user gets their own subdirectory for their data.
        $this->dataDir = __DIR__ . '/../data/user_data/' . preg_replace('/[^a-zA-Z0-9_-]/', '', $userId);
        if (!is_dir($this->dataDir)) {
            mkdir($this->dataDir, 0755, true);
        }
    }

    public function saveBricks($bricks) {
        $file = $this->dataDir . '/bricks.json';
        return file_put_contents($file, json_encode($bricks, JSON_PRETTY_PRINT));
    }

    public function loadBricks() {
        $file = $this->dataDir . '/bricks.json';
        if (!file_exists($file)) {
            return []; // Return empty array if no bricks exist yet
        }
        $content = file_get_contents($file);
        return json_decode($content, true);
    }

    public function deleteBrick($brickId) {
        $bricks = $this->loadBricks();
        $bricks = array_filter($bricks, function($brick) use ($brickId) {
            return $brick['id'] !== $brickId;
        });
        return $this->saveBricks(array_values($bricks));
    }

    public function updateBrick($brickId, $updatedBrick) {
        $bricks = $this->loadBricks();
        foreach ($bricks as &$brick) {
            if ($brick['id'] === $brickId) {
                $brick = array_merge($brick, $updatedBrick);
                break;
            }
        }
        return $this->saveBricks($bricks);
    }

    public function cloneBrick($brickId) {
        $bricks = $this->loadBricks();
        foreach ($bricks as $brick) {
            if ($brick['id'] === $brickId) {
                $clonedBrick = $brick;
                $clonedBrick['id'] = uniqid('brick_');
                $clonedBrick['brickName'] = $brick['brickName'] . ' (Copy)';
                $bricks[] = $clonedBrick;
                $this->saveBricks($bricks);
                return $clonedBrick;
            }
        }
        return false;
    }

    // === STORY MANAGEMENT METHODS ===

    public function saveStories($stories) {
        $file = $this->dataDir . '/stories.json';
        return file_put_contents($file, json_encode($stories, JSON_PRETTY_PRINT));
    }

    public function loadStories() {
        $file = $this->dataDir . '/stories.json';
        if (!file_exists($file)) {
            return [];
        }
        $content = file_get_contents($file);
        return json_decode($content, true);
    }

    public function saveStory($story) {
        $stories = $this->loadStories();
        $story['id'] = uniqid('story_');
        $story['created_at'] = date('Y-m-d H:i:s');
        $story['updated_at'] = date('Y-m-d H:i:s');
        $stories[] = $story;
        $this->saveStories($stories);
        return $story;
    }

    public function updateStory($storyId, $updatedStory) {
        $stories = $this->loadStories();
        foreach ($stories as &$story) {
            if ($story['id'] === $storyId) {
                $story = array_merge($story, $updatedStory);
                $story['updated_at'] = date('Y-m-d H:i:s');
                break;
            }
        }
        return $this->saveStories($stories);
    }

    public function deleteStory($storyId) {
        $stories = $this->loadStories();
        $stories = array_filter($stories, function($story) use ($storyId) {
            return $story['id'] !== $storyId;
        });
        return $this->saveStories(array_values($stories));
    }

    public function cloneStory($storyId) {
        $stories = $this->loadStories();
        foreach ($stories as $story) {
            if ($story['id'] === $storyId) {
                $clonedStory = $story;
                $clonedStory['id'] = uniqid('story_');
                $clonedStory['title'] = $story['title'] . ' (Copy)';
                $clonedStory['created_at'] = date('Y-m-d H:i:s');
                $clonedStory['updated_at'] = date('Y-m-d H:i:s');
                $stories[] = $clonedStory;
                $this->saveStories($stories);
                return $clonedStory;
            }
        }
        return false;
    }
}
?>
