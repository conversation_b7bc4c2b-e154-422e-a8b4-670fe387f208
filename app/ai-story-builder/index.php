<?php
// This assumes the dashboard provides a session or user authentication mechanism.
// For now, we'll use a hardcoded user ID for development.
session_start();
$userId = $_SESSION['user_id'] ?? 'default_user';

require_once 'includes/DataStorage.php';

// Instantiate data storage for the current user
$dataStorage = new DataStorage($userId);
$initialBricks = $dataStorage->loadBricks();
$initialStories = $dataStorage->loadStories();

// Load the main template
require_once 'templates/main.php';
?>
