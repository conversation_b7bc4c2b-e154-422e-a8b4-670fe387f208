{"app": {"id": "sample-app", "name": "Sample App", "description": "A production-ready example demonstrating AI Dashboard integration with chat, data management, and comprehensive features", "version": "1.0.0", "author": "AI Dashboard Team", "license": "MIT", "homepage": "https://github.com/ai-dashboard/sample-app", "status": "production"}, "dashboard": {"min_version": "1.0.0", "integration_version": "1.0.0", "requires_auth": true, "requires_admin": false, "menu_item": {"title": "Sample App", "icon": "app-sample", "position": 100, "visible": true}}, "features": {"ai_integration": true, "chat_interface": true, "flat_file_storage": true, "api_endpoints": true, "real_time": false, "notifications": true}, "ai": {"models": ["gpt-4", "gpt-3.5-turbo", "claude-3"], "default_model": "gpt-3.5-turbo", "max_tokens": 2000, "temperature": 0.7, "system_prompt": "You are a helpful assistant integrated with the AI Dashboard sample app. Provide clear, concise responses and help users understand the app's capabilities."}, "storage": {"type": "flat_file", "directories": [{"name": "chats", "description": "Chat conversations with AI"}, {"name": "user_data", "description": "App-specific user data storage"}, {"name": "settings", "description": "App configuration settings"}]}, "api": {"endpoints": [{"path": "/api/chat.php", "method": "POST", "description": "AI chat endpoint", "auth_required": true}, {"path": "/api/data.php", "method": "GET|POST|PUT|DELETE", "description": "Data management endpoint", "auth_required": true}]}, "permissions": {"view": ["user", "admin"], "edit": ["user", "admin"], "admin": ["admin"], "api": ["user", "admin"]}, "backup": {"include_files": true, "include_data": true, "exclude_patterns": ["logs/*", "temp/*", "cache/*", "data/test.json"]}, "logging": {"level": "info", "max_file_size": "10MB", "retention_days": 30, "categories": ["chat", "data", "api", "auth", "error"]}, "update": {"check_url": "https://api.example.com/sample-app/updates", "auto_backup": true, "require_approval": false, "rollback_enabled": true}, "dependencies": {"php": ">=7.4", "dashboard": ">=1.0.0", "extensions": ["curl", "json"]}, "assets": {"css": [], "js": ["assets/js/app.js", "assets/js/chat.js"]}, "routes": {"/": "index.php", "/chat": "templates/chat.php", "/data": "templates/data.php", "/settings": "templates/settings.php", "/analytics": "templates/analytics.php"}}