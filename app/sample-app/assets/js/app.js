/**
 * Sample App - Main JavaScript
 * 
 * Core JavaScript functionality for the sample app
 */

// App namespace
window.SampleApp = {
    config: {},
    api: {},
    ui: {},
    utils: {}
};

// Initialize app
document.addEventListener('DOMContentLoaded', function() {
    SampleApp.init();
});

/**
 * Initialize the sample app
 */
SampleApp.init = function() {
    // Load configuration
    this.loadConfig();

    // Initialize API client
    this.api.init();

    // Initialize UI components
    this.ui.init();

    // Log app start
    this.logActivity('app.initialized', 'Sample app initialized');
};

/**
 * Load app configuration
 */
SampleApp.loadConfig = function() {
    // Default configuration
    this.config = {
        apiBaseUrl: 'api/',
        debug: false,
        autoSave: true,
        theme: 'auto'
    };
    
    // Load from localStorage if available
    const savedConfig = localStorage.getItem('sampleApp.config');
    if (savedConfig) {
        try {
            Object.assign(this.config, JSON.parse(savedConfig));
        } catch (e) {
            console.warn('Failed to load saved config:', e);
        }
    }
};

/**
 * Save configuration
 */
SampleApp.saveConfig = function() {
    localStorage.setItem('sampleApp.config', JSON.stringify(this.config));
};

/**
 * API Client
 */
SampleApp.api.init = function() {
    this.baseUrl = SampleApp.config.apiBaseUrl;
};

/**
 * Make API request
 */
SampleApp.api.request = async function(endpoint, options = {}) {
    const url = this.baseUrl + endpoint;
    const defaultOptions = {
        headers: {
            'Content-Type': 'application/json'
        }
    };
    
    const requestOptions = Object.assign(defaultOptions, options);
    
    try {
        const response = await fetch(url, requestOptions);
        const data = await response.json();
        
        if (!response.ok) {
            throw new Error(data.error || `HTTP ${response.status}`);
        }
        
        return data;
    } catch (error) {
        console.error('API request failed:', error);
        throw error;
    }
};

/**
 * Chat API methods
 */
SampleApp.api.chat = {
    send: function(message, conversationId = null, context = {}) {
        return SampleApp.api.request('chat.php', {
            method: 'POST',
            body: JSON.stringify({
                message: message,
                conversation_id: conversationId,
                context: context
            })
        });
    },
    
    getConversations: function(limit = 20) {
        return SampleApp.api.request(`chat.php?limit=${limit}`);
    },
    
    getConversation: function(conversationId, limit = 50) {
        return SampleApp.api.request(`chat.php?conversation_id=${conversationId}&limit=${limit}`);
    }
};

/**
 * Data API methods
 */
SampleApp.api.data = {
    get: function(type, key = null) {
        const params = new URLSearchParams({ type });
        if (key) params.append('key', key);
        return SampleApp.api.request(`data.php?${params}`);
    },
    
    create: function(type, key, value, metadata = {}) {
        return SampleApp.api.request('data.php', {
            method: 'POST',
            body: JSON.stringify({
                type: type,
                key: key,
                value: value,
                metadata: metadata
            })
        });
    },
    
    update: function(type, key, value, metadata = {}) {
        return SampleApp.api.request('data.php', {
            method: 'PUT',
            body: JSON.stringify({
                type: type,
                key: key,
                value: value,
                metadata: metadata
            })
        });
    },
    
    delete: function(type, key) {
        const params = new URLSearchParams({ type, key });
        return SampleApp.api.request(`data.php?${params}`, {
            method: 'DELETE'
        });
    }
};

/**
 * UI Components
 */
SampleApp.ui.init = function() {
    this.initTooltips();
    this.initModals();
    this.initNotifications();
};

/**
 * Initialize tooltips
 */
SampleApp.ui.initTooltips = function() {
    const tooltips = document.querySelectorAll('[data-tooltip]');
    tooltips.forEach(element => {
        element.addEventListener('mouseenter', function() {
            SampleApp.ui.showTooltip(this, this.dataset.tooltip);
        });
        
        element.addEventListener('mouseleave', function() {
            SampleApp.ui.hideTooltip();
        });
    });
};

/**
 * Show tooltip
 */
SampleApp.ui.showTooltip = function(element, text) {
    const tooltip = document.createElement('div');
    tooltip.id = 'sample-app-tooltip';
    tooltip.className = 'absolute z-50 px-2 py-1 text-sm bg-gray-900 text-white rounded shadow-lg';
    tooltip.textContent = text;
    
    document.body.appendChild(tooltip);
    
    const rect = element.getBoundingClientRect();
    tooltip.style.left = rect.left + (rect.width / 2) - (tooltip.offsetWidth / 2) + 'px';
    tooltip.style.top = rect.top - tooltip.offsetHeight - 5 + 'px';
};

/**
 * Hide tooltip
 */
SampleApp.ui.hideTooltip = function() {
    const tooltip = document.getElementById('sample-app-tooltip');
    if (tooltip) {
        tooltip.remove();
    }
};

/**
 * Initialize modals
 */
SampleApp.ui.initModals = function() {
    // Modal functionality would go here
};

/**
 * Show notification
 */
SampleApp.ui.showNotification = function(message, type = 'info', duration = 5000) {
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg max-w-sm ${
        type === 'success' ? 'bg-green-500 text-white' :
        type === 'error' ? 'bg-red-500 text-white' :
        type === 'warning' ? 'bg-yellow-500 text-black' :
        'bg-blue-500 text-white'
    }`;
    
    notification.innerHTML = `
        <div class="flex items-center justify-between">
            <span>${message}</span>
            <button onclick="this.parentElement.parentElement.remove()" class="ml-4 text-lg">&times;</button>
        </div>
    `;
    
    document.body.appendChild(notification);
    
    // Auto-remove after duration
    if (duration > 0) {
        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, duration);
    }
};

/**
 * Initialize notifications
 */
SampleApp.ui.initNotifications = function() {
    // Notification system initialization
};

/**
 * Utility functions
 */
SampleApp.utils.formatDate = function(date) {
    return new Date(date).toLocaleDateString();
};

SampleApp.utils.formatTime = function(date) {
    return new Date(date).toLocaleTimeString();
};

SampleApp.utils.formatDateTime = function(date) {
    return new Date(date).toLocaleString();
};

SampleApp.utils.debounce = function(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
};

SampleApp.utils.throttle = function(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
};

/**
 * Log activity
 */
SampleApp.logActivity = function(action, description, data = {}) {
    // Send to server if needed
    // This could integrate with the dashboard's activity logging
};

/**
 * Error handling
 */
SampleApp.handleError = function(error, context = '') {
    console.error('Sample App Error:', context, error);
    
    // Show user-friendly error message
    this.ui.showNotification(
        'An error occurred. Please try again.',
        'error'
    );
    
    // Log error activity
    this.logActivity('app.error', 'Error occurred', {
        error: error.message,
        context: context,
        timestamp: new Date().toISOString()
    });
};

// Global error handler
window.addEventListener('error', function(event) {
    SampleApp.handleError(event.error, 'Global error handler');
});

// Unhandled promise rejection handler
window.addEventListener('unhandledrejection', function(event) {
    SampleApp.handleError(event.reason, 'Unhandled promise rejection');
});

// Export for use in other scripts
window.SampleApp = SampleApp;
