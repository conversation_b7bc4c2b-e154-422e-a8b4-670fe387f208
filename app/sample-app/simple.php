<?php
/**
 * Simple Sample App Test - No Authentication Required
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sample App - Simple Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100">
    <div class="max-w-6xl mx-auto p-6">
        <h1 class="text-3xl font-bold text-gray-900 mb-6">📱 Sample App - Simple Test</h1>
        
        <div class="bg-white rounded-lg shadow p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4">Testing App Components</h2>
            
            <?php
            try {
                echo "<div class='mb-4'>";
                echo "<h3 class='font-medium text-green-600'>✅ Basic PHP Working</h3>";
                echo "<p class='text-sm text-gray-600'>PHP version: " . PHP_VERSION . "</p>";
                echo "</div>";
                
                // Test config loading
                echo "<div class='mb-4'>";
                $configFile = __DIR__ . '/config.json';
                if (file_exists($configFile)) {
                    $config = json_decode(file_get_contents($configFile), true);
                    echo "<h3 class='font-medium text-green-600'>✅ Config File Loaded</h3>";
                    echo "<p class='text-sm text-gray-600'>App: " . htmlspecialchars($config['app']['name']) . "</p>";
                    echo "<p class='text-sm text-gray-600'>Version: " . htmlspecialchars($config['app']['version']) . "</p>";
                } else {
                    echo "<h3 class='font-medium text-red-600'>❌ Config File Missing</h3>";
                }
                echo "</div>";
                
                // Test data storage (flat file system)
                echo "<div class='mb-4'>";
                try {
                    $dataDir = __DIR__ . '/data';
                    if (is_dir($dataDir) && is_writable($dataDir)) {
                        echo "<h3 class='font-medium text-green-600'>✅ Flat File Storage Working</h3>";
                        echo "<p class='text-sm text-gray-600'>Data directory: " . $dataDir . "</p>";

                        // Test write/read
                        $testFile = $dataDir . '/test.json';
                        $testData = ['test' => true, 'timestamp' => time()];
                        if (file_put_contents($testFile, json_encode($testData))) {
                            echo "<p class='text-sm text-gray-600'>✅ Write test successful</p>";
                            if (file_exists($testFile)) {
                                unlink($testFile); // Clean up
                                echo "<p class='text-sm text-gray-600'>✅ Read/delete test successful</p>";
                            }
                        }
                    } else {
                        echo "<h3 class='font-medium text-yellow-600'>⚠️ Data Storage Issue</h3>";
                        echo "<p class='text-sm text-gray-600'>Data directory not writable or missing</p>";
                    }
                } catch (Exception $e) {
                    echo "<h3 class='font-medium text-yellow-600'>⚠️ Data Storage Issue</h3>";
                    echo "<p class='text-sm text-gray-600'>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
                }
                echo "</div>";
                
                // Test file permissions
                echo "<div class='mb-4'>";
                $logsDir = __DIR__ . '/logs';
                if (is_dir($logsDir) && is_writable($logsDir)) {
                    echo "<h3 class='font-medium text-green-600'>✅ Logs Directory Writable</h3>";
                } else {
                    echo "<h3 class='font-medium text-yellow-600'>⚠️ Logs Directory Not Writable</h3>";
                }
                echo "</div>";
                
                // Test includes
                echo "<div class='mb-4'>";
                $requiredFiles = [
                    'includes/app.php',
                    'includes/ai-integration.php',
                    'includes/data-storage.php'
                ];
                
                $allFilesExist = true;
                foreach ($requiredFiles as $file) {
                    if (!file_exists(__DIR__ . '/' . $file)) {
                        $allFilesExist = false;
                        break;
                    }
                }
                
                if ($allFilesExist) {
                    echo "<h3 class='font-medium text-green-600'>✅ All Required Files Present</h3>";
                } else {
                    echo "<h3 class='font-medium text-red-600'>❌ Missing Required Files</h3>";
                }
                echo "</div>";
                
            } catch (Exception $e) {
                echo "<div class='mb-4'>";
                echo "<h3 class='font-medium text-red-600'>❌ Error During Testing</h3>";
                echo "<p class='text-sm text-gray-600'>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
                echo "<p class='text-sm text-gray-600'>File: " . htmlspecialchars($e->getFile()) . "</p>";
                echo "<p class='text-sm text-gray-600'>Line: " . $e->getLine() . "</p>";
                echo "</div>";
            }
            ?>
        </div>
        
        <div class="bg-white rounded-lg shadow p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4">App Features</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div class="border border-gray-200 rounded-lg p-4">
                    <h3 class="font-medium text-gray-900 mb-2">🤖 AI Integration</h3>
                    <p class="text-sm text-gray-600">Chat interface with AI models</p>
                    <a href="templates/chat.php" class="text-blue-600 text-sm hover:underline">Test Chat →</a>
                </div>

                <div class="border border-gray-200 rounded-lg p-4">
                    <h3 class="font-medium text-gray-900 mb-2">🗄️ Flat File Storage</h3>
                    <p class="text-sm text-gray-600">JSON-based data storage and management</p>
                    <a href="api/data.php" class="text-blue-600 text-sm hover:underline">Test API →</a>
                </div>

                <div class="border border-gray-200 rounded-lg p-4">
                    <h3 class="font-medium text-gray-900 mb-2">📊 Dashboard Integration</h3>
                    <p class="text-sm text-gray-600">Theme and auth integration</p>
                    <a href="index.php" class="text-blue-600 text-sm hover:underline">Full App →</a>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg shadow p-6">
            <h2 class="text-xl font-semibold mb-4">Next Steps</h2>
            
            <div class="space-y-3">
                <div class="flex items-start space-x-3">
                    <div class="text-green-500">1.</div>
                    <div>
                        <div class="font-medium">Fix any issues shown above</div>
                        <div class="text-sm text-gray-600">Ensure flat file storage and file permissions are working</div>
                    </div>
                </div>
                
                <div class="flex items-start space-x-3">
                    <div class="text-green-500">2.</div>
                    <div>
                        <div class="font-medium">Configure AI API</div>
                        <div class="text-sm text-gray-600">Set up OpenAI or Anthropic API keys in dashboard settings</div>
                    </div>
                </div>
                
                <div class="flex items-start space-x-3">
                    <div class="text-green-500">3.</div>
                    <div>
                        <div class="font-medium">Test Full Integration</div>
                        <div class="text-sm text-gray-600">Try the full app with authentication and all features</div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="mt-6 text-center">
            <a href="../../dashboard.php" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition duration-200">
                Back to Dashboard
            </a>
        </div>
    </div>
</body>
</html>
