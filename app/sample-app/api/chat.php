<?php
/**
 * Sample App - AI Chat API Endpoint
 *
 * This endpoint handles AI chat requests and demonstrates
 * integration with the dashboard's AI system.
 */

// Increase execution time for AI requests
set_time_limit(120); // 2 minutes
ini_set('max_execution_time', 120);

// Include dashboard authentication and configuration
require_once __DIR__ . '/../../../auth.php';
require_once __DIR__ . '/../../../config.php';

// Require authentication
requireAuth();

// Include app files
require_once __DIR__ . '/../includes/app.php';

// Set JSON response header
header('Content-Type: application/json');

// Get the app instance
$app = $GLOBALS['sample_app'];
$ai = $app->getAI();
$dataStorage = $app->getDataStorage();

// Handle different HTTP methods
$method = $_SERVER['REQUEST_METHOD'];

switch ($method) {
    case 'POST':
        handleChatRequest();
        break;
    case 'GET':
        handleGetConversations();
        break;
    case 'DELETE':
        handleDeleteConversation();
        break;
    default:
        http_response_code(405);
        echo json_encode(['error' => 'Method not allowed']);
        break;
}

/**
 * Handle chat request
 */
function handleChatRequest() {
    global $app, $ai, $dataStorage;
    
    try {
        // Get request data
        $input = json_decode(file_get_contents('php://input'), true);
        
        if (!$input) {
            throw new Exception('Invalid JSON input');
        }
        
        $message = $input['message'] ?? '';
        $conversationId = $input['conversation_id'] ?? uniqid('conv_');
        $context = $input['context'] ?? [];
        
        if (empty($message)) {
            throw new Exception('Message is required');
        }
        
        // Get conversation history for context
        $history = $dataStorage->getChatHistory($conversationId, 10);
        
        // Build context with conversation history
        $enhancedContext = array_merge($context, [
            'conversation_id' => $conversationId,
            'conversation_history' => array_slice($history, -5), // Last 5 messages
            'user_id' => $_SESSION['user_id'],
            'app' => 'sample-app'
        ]);
        
        // Send request to AI
        $response = $ai->chat($message, $enhancedContext);

        if (!$response['success']) {
            throw new Exception('AI request failed');
        }
        
        // Save to data storage
        $dataStorage->saveChatMessage(
            $_SESSION['user_id'],
            $conversationId,
            $message,
            $response['response'],
            $response['model'],
            $response['usage']['total_tokens'] ?? 0
        );
        
        // Log activity
        $app->logActivity('chat.message_sent', 'AI chat message processed', [
            'conversation_id' => $conversationId,
            'message_length' => strlen($message),
            'response_length' => strlen($response['response']),
            'model' => $response['model'],
            'tokens_used' => $response['usage']['total_tokens'] ?? 0
        ]);
        
        // Return response
        echo json_encode([
            'success' => true,
            'response' => $response['response'],
            'conversation_id' => $conversationId,
            'model' => $response['model'],
            'usage' => $response['usage'] ?? [],
            'timestamp' => date('Y-m-d H:i:s')
        ]);
        
    } catch (Exception $e) {
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'error' => $e->getMessage()
        ]);
        
        // Log error
        $app->logActivity('chat.error', 'Chat request failed', [
            'error' => $e->getMessage(),
            'user_id' => $_SESSION['user_id'] ?? null
        ]);
    }
}

/**
 * Handle get conversations request
 */
function handleGetConversations() {
    global $app, $dataStorage;
    
    try {
        $userId = $_SESSION['user_id'];
        $limit = $_GET['limit'] ?? 20;
        $conversationId = $_GET['conversation_id'] ?? null;
        
        if ($conversationId) {
            // Get specific conversation history
            $messages = $dataStorage->getChatHistory($conversationId, $limit);
            
            echo json_encode([
                'success' => true,
                'conversation_id' => $conversationId,
                'messages' => $messages,
                'count' => count($messages)
            ]);
        } else {
            // Get user's conversations
            $conversations = $dataStorage->getUserConversations($userId, $limit);
            
            echo json_encode([
                'success' => true,
                'conversations' => $conversations,
                'count' => count($conversations)
            ]);
        }
        
    } catch (Exception $e) {
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'error' => $e->getMessage()
        ]);
        
        // Log error
        $app->logActivity('chat.get_error', 'Failed to retrieve conversations', [
            'error' => $e->getMessage(),
            'user_id' => $_SESSION['user_id'] ?? null
        ]);
    }
}

/**
 * Handle delete conversation request
 */
function handleDeleteConversation() {
    global $app, $dataStorage;

    try {
        $userId = $_SESSION['user_id'];

        // Check if this is a request to clear all conversations
        $input = json_decode(file_get_contents('php://input'), true);
        $action = $input['action'] ?? null;

        if ($action === 'clear_all') {
            // Clear all conversations for the user
            $result = $dataStorage->clearAllConversations($userId);

            if ($result) {
                // Log activity
                $app->logActivity('chat.all_conversations_cleared', 'All conversations cleared', [
                    'user_id' => $userId
                ]);

                echo json_encode([
                    'success' => true,
                    'message' => 'All conversations cleared successfully'
                ]);
            } else {
                throw new Exception('Failed to clear all conversations');
            }
        } else {
            // Delete single conversation
            $conversationId = $_GET['conversation_id'] ?? null;

            if (!$conversationId) {
                throw new Exception('Conversation ID is required');
            }

            // Delete the conversation
            $result = $dataStorage->deleteConversation($userId, $conversationId);

            if ($result) {
                // Log activity
                $app->logActivity('chat.conversation_deleted', 'Conversation deleted', [
                    'conversation_id' => $conversationId,
                    'user_id' => $userId
                ]);

                echo json_encode([
                    'success' => true,
                    'message' => 'Conversation deleted successfully'
                ]);
            } else {
                throw new Exception('Failed to delete conversation');
            }
        }

    } catch (Exception $e) {
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'error' => $e->getMessage()
        ]);

        // Log error
        $app->logActivity('chat.delete_error', 'Failed to delete conversation(s)', [
            'error' => $e->getMessage(),
            'conversation_id' => $_GET['conversation_id'] ?? null,
            'user_id' => $_SESSION['user_id'] ?? null
        ]);
    }
}
?>
