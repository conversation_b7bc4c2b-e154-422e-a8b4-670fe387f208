<?php
/**
 * Sample App - Flat File Data Storage
 *
 * This class handles flat file data operations for the sample app,
 * following the same pattern as the AI Dashboard.
 */

class SampleAppData {
    private $config;
    private $dataDir;

    public function __construct($appConfig) {
        $this->config = $appConfig;
        $this->dataDir = dirname(__DIR__) . '/data';
        $this->initializeDataDirectory();
    }

    /**
     * Initialize data directory structure
     */
    private function initializeDataDirectory() {
        $directories = [
            $this->dataDir,
            $this->dataDir . '/chats',
            $this->dataDir . '/user_data',
            $this->dataDir . '/settings'
        ];

        foreach ($directories as $dir) {
            if (!is_dir($dir)) {
                mkdir($dir, 0755, true);
            }
        }

        // Create .htaccess to protect data directory
        $htaccessFile = $this->dataDir . '/.htaccess';
        if (!file_exists($htaccessFile)) {
            file_put_contents($htaccessFile, "Deny from all\n");
        }
    }
    
    /**
     * Initialize data storage
     */
    public function initializeStorage() {
        // Data storage is already initialized in constructor
        return true;
    }

    /**
     * Load data from JSON file
     */
    private function loadJsonFile($filePath) {
        if (!file_exists($filePath)) {
            return [];
        }

        $content = file_get_contents($filePath);
        $data = json_decode($content, true);

        return is_array($data) ? $data : [];
    }

    /**
     * Save data to JSON file
     */
    private function saveJsonFile($filePath, $data) {
        $dir = dirname($filePath);
        if (!is_dir($dir)) {
            mkdir($dir, 0755, true);
        }

        return file_put_contents($filePath, json_encode($data, JSON_PRETTY_PRINT)) !== false;
    }

    /**
     * Generate unique ID
     */
    private function generateId() {
        return uniqid('', true);
    }
    
    /**
     * Save chat message
     */
    public function saveChatMessage($userId, $conversationId, $message, $response, $aiModel, $tokensUsed = 0) {
        $chatFile = $this->dataDir . '/chats/' . $conversationId . '.json';
        $chats = $this->loadJsonFile($chatFile);

        $newMessage = [
            'id' => $this->generateId(),
            'user_id' => $userId,
            'conversation_id' => $conversationId,
            'message' => $message,
            'response' => $response,
            'ai_model' => $aiModel,
            'tokens_used' => $tokensUsed,
            'created_at' => date('Y-m-d H:i:s')
        ];

        $chats[] = $newMessage;

        return $this->saveJsonFile($chatFile, $chats);
    }

    /**
     * Get chat history for a conversation
     */
    public function getChatHistory($conversationId, $limit = 50) {
        $chatFile = $this->dataDir . '/chats/' . $conversationId . '.json';
        $chats = $this->loadJsonFile($chatFile);

        // Sort by created_at and limit
        usort($chats, function($a, $b) {
            return strtotime($a['created_at']) - strtotime($b['created_at']);
        });

        return array_slice($chats, -$limit);
    }

    /**
     * Get user's conversations
     */
    public function getUserConversations($userId, $limit = 20) {
        $conversations = [];
        $chatDir = $this->dataDir . '/chats';

        if (!is_dir($chatDir)) {
            return [];
        }

        $files = glob($chatDir . '/*.json');

        foreach ($files as $file) {
            $chats = $this->loadJsonFile($file);
            $userChats = array_filter($chats, function($chat) use ($userId) {
                return $chat['user_id'] === $userId;
            });

            if (!empty($userChats)) {
                $conversationId = basename($file, '.json');
                $lastChat = end($userChats);
                $firstChat = reset($userChats);

                $conversations[] = [
                    'conversation_id' => $conversationId,
                    'message_count' => count($userChats),
                    'last_activity' => $lastChat['created_at'],
                    'preview' => substr($firstChat['message'], 0, 100)
                ];
            }
        }

        // Sort by last activity
        usort($conversations, function($a, $b) {
            return strtotime($b['last_activity']) - strtotime($a['last_activity']);
        });

        return array_slice($conversations, 0, $limit);
    }

    /**
     * Delete a conversation
     */
    public function deleteConversation($userId, $conversationId) {
        $chatFile = $this->dataDir . '/chats/' . $conversationId . '.json';

        if (!file_exists($chatFile)) {
            return false; // Conversation doesn't exist
        }

        // Load the conversation
        $chats = $this->loadJsonFile($chatFile);

        // Filter out messages from this user
        $remainingChats = array_filter($chats, function($chat) use ($userId) {
            return $chat['user_id'] !== $userId;
        });

        if (empty($remainingChats)) {
            // If no messages remain, delete the entire file
            return unlink($chatFile);
        } else {
            // Save the remaining messages
            return $this->saveJsonFile($chatFile, array_values($remainingChats));
        }
    }

    /**
     * Clear all conversations for a user
     */
    public function clearAllConversations($userId) {
        $chatDir = $this->dataDir . '/chats';

        if (!is_dir($chatDir)) {
            return true; // No chat directory means no conversations to clear
        }

        $files = glob($chatDir . '/*.json');
        $success = true;

        foreach ($files as $file) {
            $chats = $this->loadJsonFile($file);

            // Filter out messages from this user
            $remainingChats = array_filter($chats, function($chat) use ($userId) {
                return $chat['user_id'] !== $userId;
            });

            if (empty($remainingChats)) {
                // If no messages remain, delete the entire file
                if (!unlink($file)) {
                    $success = false;
                }
            } else {
                // Save the remaining messages
                if (!$this->saveJsonFile($file, array_values($remainingChats))) {
                    $success = false;
                }
            }
        }

        return $success;
    }

    /**
     * Store app data
     */
    public function storeData($userId, $dataType, $dataKey, $dataValue, $metadata = []) {
        $dataFile = $this->dataDir . '/user_data/' . $userId . '_' . $dataType . '.json';
        $data = $this->loadJsonFile($dataFile);

        $newData = [
            'data_key' => $dataKey,
            'data_value' => $dataValue,
            'metadata' => $metadata,
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];

        // Find existing entry and update, or add new
        $found = false;
        foreach ($data as &$item) {
            if ($item['data_key'] === $dataKey) {
                $newData['created_at'] = $item['created_at']; // Keep original creation time
                $item = $newData;
                $found = true;
                break;
            }
        }

        if (!$found) {
            $data[] = $newData;
        }

        return $this->saveJsonFile($dataFile, $data);
    }

    /**
     * Retrieve app data
     */
    public function getData($userId, $dataType, $dataKey = null) {
        $dataFile = $this->dataDir . '/user_data/' . $userId . '_' . $dataType . '.json';
        $data = $this->loadJsonFile($dataFile);

        if ($dataKey) {
            // Return specific data item
            foreach ($data as $item) {
                if ($item['data_key'] === $dataKey) {
                    return $item;
                }
            }
            return null;
        } else {
            // Return all data of this type, sorted by updated_at
            usort($data, function($a, $b) {
                return strtotime($b['updated_at']) - strtotime($a['updated_at']);
            });
            return $data;
        }
    }

    /**
     * Delete app data
     */
    public function deleteData($userId, $dataType, $dataKey = null) {
        $dataFile = $this->dataDir . '/user_data/' . $userId . '_' . $dataType . '.json';

        if ($dataKey) {
            // Delete specific data item
            $data = $this->loadJsonFile($dataFile);
            $data = array_filter($data, function($item) use ($dataKey) {
                return $item['data_key'] !== $dataKey;
            });
            return $this->saveJsonFile($dataFile, array_values($data));
        } else {
            // Delete entire data file
            if (file_exists($dataFile)) {
                return unlink($dataFile);
            }
            return true;
        }
    }
    
    /**
     * Save app setting
     */
    public function saveSetting($settingKey, $settingValue, $userId = null, $isGlobal = false) {
        $settingsFile = $isGlobal
            ? $this->dataDir . '/settings/global.json'
            : $this->dataDir . '/settings/user_' . $userId . '.json';

        $settings = $this->loadJsonFile($settingsFile);

        $settings[$settingKey] = [
            'value' => $settingValue,
            'updated_at' => date('Y-m-d H:i:s'),
            'is_global' => $isGlobal
        ];

        return $this->saveJsonFile($settingsFile, $settings);
    }

    /**
     * Get app setting
     */
    public function getSetting($settingKey, $userId = null) {
        // Try user-specific setting first
        if ($userId) {
            $userSettingsFile = $this->dataDir . '/settings/user_' . $userId . '.json';
            $userSettings = $this->loadJsonFile($userSettingsFile);

            if (isset($userSettings[$settingKey])) {
                return $userSettings[$settingKey]['value'];
            }
        }

        // Fall back to global setting
        $globalSettingsFile = $this->dataDir . '/settings/global.json';
        $globalSettings = $this->loadJsonFile($globalSettingsFile);

        if (isset($globalSettings[$settingKey])) {
            return $globalSettings[$settingKey]['value'];
        }

        return null;
    }
    
    /**
     * Get app statistics
     */
    public function getStatistics() {
        $stats = [];

        // Chat statistics
        $chatDir = $this->dataDir . '/chats';
        $totalChats = 0;
        $totalTokens = 0;
        $uniqueUsers = [];
        $conversations = 0;

        if (is_dir($chatDir)) {
            $chatFiles = glob($chatDir . '/*.json');
            $conversations = count($chatFiles);

            foreach ($chatFiles as $file) {
                $chats = $this->loadJsonFile($file);
                $totalChats += count($chats);

                foreach ($chats as $chat) {
                    $uniqueUsers[$chat['user_id']] = true;
                    $totalTokens += $chat['tokens_used'] ?? 0;
                }
            }
        }

        $stats['chats'] = [
            'total_chats' => $totalChats,
            'unique_users' => count($uniqueUsers),
            'conversations' => $conversations,
            'total_tokens' => $totalTokens
        ];

        // Data statistics
        $dataDir = $this->dataDir . '/user_data';
        $totalRecords = 0;
        $dataUniqueUsers = [];
        $dataTypes = [];

        if (is_dir($dataDir)) {
            $dataFiles = glob($dataDir . '/*.json');

            foreach ($dataFiles as $file) {
                $data = $this->loadJsonFile($file);
                $totalRecords += count($data);

                $filename = basename($file, '.json');
                $parts = explode('_', $filename, 2);
                if (count($parts) === 2) {
                    $dataUniqueUsers[$parts[0]] = true;
                    $dataTypes[$parts[1]] = true;
                }
            }
        }

        $stats['data'] = [
            'total_records' => $totalRecords,
            'unique_users' => count($dataUniqueUsers),
            'data_types' => count($dataTypes)
        ];

        // Settings statistics
        $settingsDir = $this->dataDir . '/settings';
        $totalSettings = 0;
        $globalSettings = 0;
        $userSettings = 0;

        if (is_dir($settingsDir)) {
            $settingsFiles = glob($settingsDir . '/*.json');

            foreach ($settingsFiles as $file) {
                $settings = $this->loadJsonFile($file);
                $totalSettings += count($settings);

                if (basename($file) === 'global.json') {
                    $globalSettings += count($settings);
                } else {
                    $userSettings += count($settings);
                }
            }
        }

        $stats['settings'] = [
            'total_settings' => $totalSettings,
            'global_settings' => $globalSettings,
            'user_settings' => $userSettings
        ];

        return $stats;
    }

    /**
     * Test data storage
     */
    public function testConnection() {
        try {
            // Test if we can write to the data directory
            $testFile = $this->dataDir . '/test.json';
            $testData = ['test' => true, 'timestamp' => time()];

            if (!$this->saveJsonFile($testFile, $testData)) {
                throw new Exception('Cannot write to data directory');
            }

            // Test if we can read from the data directory
            $readData = $this->loadJsonFile($testFile);
            if (!$readData || $readData['test'] !== true) {
                throw new Exception('Cannot read from data directory');
            }

            // Clean up test file
            unlink($testFile);

            return true;
        } catch (Exception $e) {
            throw new Exception('Data storage test failed: ' . $e->getMessage());
        }
    }

    /**
     * Get data directory path
     */
    public function getDataDir() {
        return $this->dataDir;
    }
}
?>
