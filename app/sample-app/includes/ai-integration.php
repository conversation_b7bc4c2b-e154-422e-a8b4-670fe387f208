<?php
/**
 * Sample App - AI Integration
 * 
 * This class demonstrates how to integrate with the dashboard's AI API
 * and provides AI functionality for the sample app.
 */

class SampleAppAI {
    private $config;
    private $aiConfig;
    
    public function __construct($appConfig) {
        $this->config = $appConfig;
        $this->loadAIConfig();
    }
    
    /**
     * Load AI configuration from dashboard
     */
    private function loadAIConfig() {
        // Load dashboard AI configuration
        $dashboardConfig = $this->getDashboardAIConfig();
        
        // Merge with app-specific AI config
        $this->aiConfig = array_merge(
            $dashboardConfig,
            $this->config['ai'] ?? []
        );
    }
    
    /**
     * Get dashboard AI configuration
     */
    private function getDashboardAIConfig() {
        // Include dashboard AI config functions
        require_once __DIR__ . '/../../../includes/ai_config.php';

        // Get current user's AI settings
        if (isset($_SESSION['user_id'])) {
            $userId = $_SESSION['user_id'];
            $settings = getUserAISettings($userId);

            if ($settings['active_model']) {
                $provider = getModelProvider($settings['active_model']);
                $apiKey = '';

                switch ($provider) {
                    case 'anthropic':
                        $apiKey = $settings['anthropic_api_key'];
                        break;
                    case 'google':
                        $apiKey = $settings['google_api_key'];
                        break;
                    case 'openai':
                        $apiKey = $settings['openai_api_key'];
                        break;
                }

                return [
                    'provider' => $provider,
                    'model' => $settings['active_model'],
                    'api_key' => $apiKey,
                    'max_tokens' => 8000,
                    'temperature' => 0.7,
                    'system_prompt' => 'You are a helpful AI assistant integrated with the Sample App.'
                ];
            }
        }

        // Fallback configuration
        return [
            'provider' => 'openai',
            'model' => 'gpt-3.5-turbo',
            'api_key' => '',
            'max_tokens' => 8000,
            'temperature' => 0.7,
            'system_prompt' => 'You are a helpful AI assistant.'
        ];
    }
    
    /**
     * Send AI request
     */
    public function sendRequest($message, $context = []) {
        $provider = $this->aiConfig['provider'] ?? 'openai';
        
        switch ($provider) {
            case 'openai':
                return $this->sendOpenAIRequest($message, $context);
            case 'anthropic':
                return $this->sendAnthropicRequest($message, $context);
            case 'google':
                return $this->sendGoogleRequest($message, $context);
            default:
                throw new Exception('Unsupported AI provider: ' . $provider);
        }
    }
    
    /**
     * Send OpenAI request
     */
    private function sendOpenAIRequest($message, $context = []) {
        $apiKey = $this->aiConfig['api_key'] ?? '';
        if (empty($apiKey)) {
            throw new Exception('OpenAI API key not configured');
        }
        
        $model = $this->aiConfig['model'] ?? 'gpt-3.5-turbo';
        $maxTokens = $this->aiConfig['max_tokens'] ?? 8000;
        $temperature = $this->aiConfig['temperature'] ?? 0.7;
        $systemPrompt = $this->aiConfig['system_prompt'] ?? 'You are a helpful assistant.';
        
        // Build messages array
        $messages = [
            ['role' => 'system', 'content' => $systemPrompt]
        ];
        
        // Add context if provided
        if (!empty($context)) {
            $contextMessage = "Context: " . json_encode($context);
            $messages[] = ['role' => 'system', 'content' => $contextMessage];
        }
        
        // Add user message
        $messages[] = ['role' => 'user', 'content' => $message];
        
        $data = [
            'model' => $model,
            'messages' => $messages,
            'max_tokens' => $maxTokens,
            'temperature' => $temperature
        ];
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, 'https://api.openai.com/v1/chat/completions');
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($ch, CURLOPT_TIMEOUT, 120); // 2 minutes
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 30); // 30 seconds to connect
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json',
            'Authorization: Bearer ' . $apiKey
        ]);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($httpCode !== 200) {
            throw new Exception('OpenAI API request failed with code: ' . $httpCode);
        }
        
        $result = json_decode($response, true);
        
        if (isset($result['error'])) {
            throw new Exception('OpenAI API error: ' . $result['error']['message']);
        }
        
        return [
            'success' => true,
            'response' => $result['choices'][0]['message']['content'] ?? '',
            'usage' => $result['usage'] ?? [],
            'model' => $model
        ];
    }
    
    /**
     * Send Anthropic request
     */
    private function sendAnthropicRequest($message, $context = []) {
        $apiKey = $this->aiConfig['api_key'] ?? '';
        if (empty($apiKey)) {
            throw new Exception('Anthropic API key not configured');
        }
        
        $model = $this->aiConfig['model'] ?? 'claude-3-sonnet-20240229';
        $maxTokens = $this->aiConfig['max_tokens'] ?? 8000;
        $systemPrompt = $this->aiConfig['system_prompt'] ?? 'You are a helpful assistant.';

        // Build messages array for Claude API
        $messages = [
            ['role' => 'user', 'content' => $message]
        ];

        // Add context if provided
        if (!empty($context)) {
            $contextMessage = "Context: " . json_encode($context);
            array_unshift($messages, ['role' => 'user', 'content' => $contextMessage]);
        }

        $data = [
            'model' => $model,
            'max_tokens' => $maxTokens,
            'messages' => $messages,
            'system' => $systemPrompt
        ];
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, 'https://api.anthropic.com/v1/messages');
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($ch, CURLOPT_TIMEOUT, 120); // 2 minutes
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 30); // 30 seconds to connect
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json',
            'x-api-key: ' . $apiKey,
            'anthropic-version: 2023-06-01'
        ]);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($httpCode !== 200) {
            throw new Exception('Anthropic API request failed with code: ' . $httpCode);
        }
        
        $result = json_decode($response, true);

        if (isset($result['error'])) {
            throw new Exception('Anthropic API error: ' . $result['error']['message']);
        }

        return [
            'success' => true,
            'response' => $result['content'][0]['text'] ?? '',
            'usage' => $result['usage'] ?? [],
            'model' => $model
        ];
    }

    /**
     * Send Google request
     */
    private function sendGoogleRequest($message, $context = []) {
        $apiKey = $this->aiConfig['api_key'] ?? '';
        if (empty($apiKey)) {
            throw new Exception('Google API key not configured');
        }

        $model = $this->aiConfig['model'] ?? 'gemini-pro';
        $maxTokens = $this->aiConfig['max_tokens'] ?? 8000;
        $systemPrompt = $this->aiConfig['system_prompt'] ?? 'You are a helpful assistant.';

        // Build prompt with context
        $prompt = $systemPrompt . "\n\n";

        if (!empty($context)) {
            $prompt .= "Context: " . json_encode($context) . "\n\n";
        }

        $prompt .= $message;

        $data = [
            'contents' => [
                [
                    'parts' => [
                        ['text' => $prompt]
                    ]
                ]
            ],
            'generationConfig' => [
                'maxOutputTokens' => $maxTokens
            ]
        ];

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, 'https://generativelanguage.googleapis.com/v1beta/models/' . $model . ':generateContent?key=' . $apiKey);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($ch, CURLOPT_TIMEOUT, 120); // 2 minutes
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 30); // 30 seconds to connect
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json'
        ]);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        if ($httpCode !== 200) {
            throw new Exception('Google API request failed with code: ' . $httpCode);
        }

        $result = json_decode($response, true);

        if (isset($result['error'])) {
            throw new Exception('Google API error: ' . $result['error']['message']);
        }

        return [
            'success' => true,
            'response' => $result['candidates'][0]['content']['parts'][0]['text'] ?? '',
            'usage' => $result['usageMetadata'] ?? [],
            'model' => $model
        ];
    }

    /**
     * Get available AI models
     */
    public function getAvailableModels() {
        $provider = $this->aiConfig['provider'] ?? 'openai';
        
        switch ($provider) {
            case 'openai':
                return [
                    'gpt-4' => 'GPT-4 (Most Capable)',
                    'gpt-3.5-turbo' => 'GPT-3.5 Turbo (Fast)',
                    'gpt-3.5-turbo-16k' => 'GPT-3.5 Turbo 16K (Long Context)'
                ];
            case 'anthropic':
                return [
                    'claude-3-opus-20240229' => 'Claude 3 Opus (Most Capable)',
                    'claude-3-sonnet-20240229' => 'Claude 3 Sonnet (Balanced)',
                    'claude-3-haiku-20240307' => 'Claude 3 Haiku (Fast)'
                ];
            case 'google':
                return [
                    'gemini-pro' => 'Gemini Pro (Balanced)',
                    'gemini-pro-vision' => 'Gemini Pro Vision (Multimodal)'
                ];
            default:
                return [];
        }
    }
    
    /**
     * Test AI connection
     */
    public function testConnection() {
        $apiKey = $this->aiConfig['api_key'] ?? '';
        if (empty($apiKey)) {
            throw new Exception('AI API key not configured');
        }

        try {
            // Just test if we have a valid configuration, don't make actual API call
            $provider = $this->aiConfig['provider'] ?? 'openai';
            if (!in_array($provider, ['openai', 'anthropic', 'google'])) {
                throw new Exception('Unsupported AI provider: ' . $provider);
            }
            return true;
        } catch (Exception $e) {
            throw new Exception('AI connection test failed: ' . $e->getMessage());
        }
    }
    
    /**
     * Get AI configuration
     */
    public function getConfig() {
        return $this->aiConfig;
    }
    
    /**
     * Chat with AI (convenience method)
     */
    public function chat($message, $conversationHistory = []) {
        $context = [
            'app' => 'sample-app',
            'conversation_history' => $conversationHistory,
            'timestamp' => date('Y-m-d H:i:s')
        ];
        
        return $this->sendRequest($message, $context);
    }
    
    /**
     * Generate content with AI
     */
    public function generateContent($prompt, $type = 'text') {
        $enhancedPrompt = "Generate {$type} content based on the following prompt: {$prompt}";
        
        $context = [
            'content_type' => $type,
            'app' => 'sample-app'
        ];
        
        return $this->sendRequest($enhancedPrompt, $context);
    }
    
    /**
     * Analyze data with AI
     */
    public function analyzeData($data, $analysisType = 'general') {
        $prompt = "Analyze the following data and provide insights: " . json_encode($data);
        
        $context = [
            'analysis_type' => $analysisType,
            'data_size' => count($data),
            'app' => 'sample-app'
        ];
        
        return $this->sendRequest($prompt, $context);
    }
}
?>
