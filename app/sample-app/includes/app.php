<?php
/**
 * Sample App - Main Application Class
 * 
 * This class demonstrates how to integrate a custom app with the AI Dashboard.
 * It implements the required interfaces for update management, logging, and AI integration.
 */

require_once __DIR__ . '/../../../includes/update_system.php';
require_once __DIR__ . '/../../../includes/logger.php';
require_once __DIR__ . '/ai-integration.php';
require_once __DIR__ . '/data-storage.php';

class SampleApp implements CustomAppUpdateInterface {
    private $appId = 'sample-app';
    private $appPath;
    private $config;
    private $dataStorage;
    private $aiIntegration;

    public function __construct() {
        $this->appPath = dirname(__DIR__);
        $this->loadConfig();
        $this->dataStorage = new SampleAppData($this->config);
        $this->aiIntegration = new SampleAppAI($this->config);
        $this->registerWithDashboard();
        $this->initializeStorage();
    }
    
    /**
     * Load app configuration
     */
    private function loadConfig() {
        $configFile = $this->appPath . '/config.json';
        if (file_exists($configFile)) {
            $this->config = json_decode(file_get_contents($configFile), true);
        } else {
            throw new Exception('App configuration file not found');
        }
    }
    
    /**
     * Register app with dashboard update system
     */
    private function registerWithDashboard() {
        $updateSystem = new UpdateSystem();
        
        $updateSystem->registerCustomApp($this->appId, [
            'name' => $this->config['app']['name'],
            'version' => $this->getVersion(),
            'update_handler' => [$this, 'checkForUpdates'],
            'dependencies' => $this->config['dependencies'] ?? []
        ]);
        
        $this->logActivity('app.registered', 'App registered with dashboard', [
            'app_id' => $this->appId,
            'version' => $this->getVersion()
        ]);
    }
    
    /**
     * Initialize data storage
     */
    private function initializeStorage() {
        $this->dataStorage->initializeStorage();
    }
    
    /**
     * Get current app version
     */
    public function getVersion() {
        $versionFile = $this->appPath . '/VERSION';
        if (file_exists($versionFile)) {
            return trim(file_get_contents($versionFile));
        }
        return $this->config['app']['version'] ?? '1.0.0';
    }
    
    /**
     * Check for app updates (required by CustomAppUpdateInterface)
     */
    public function checkForUpdates() {
        $updateUrl = $this->config['update']['check_url'] ?? null;
        
        if (!$updateUrl) {
            return [
                'available' => false,
                'current_version' => $this->getVersion(),
                'message' => 'No update server configured'
            ];
        }
        
        // In a real implementation, this would check the update server
        // For demo purposes, we'll simulate an update check
        $currentVersion = $this->getVersion();
        $latestVersion = '1.1.0'; // Simulated latest version
        
        $hasUpdate = version_compare($latestVersion, $currentVersion, '>');
        
        if ($hasUpdate) {
            return [
                'available' => true,
                'current_version' => $currentVersion,
                'new_version' => $latestVersion,
                'release_notes' => 'New features: Enhanced AI integration, improved chat interface, bug fixes',
                'download_url' => $updateUrl . '/v' . $latestVersion,
                'size' => '2.1 MB',
                'critical' => false,
                'compatibility' => $this->config['dependencies']
            ];
        }
        
        return [
            'available' => false,
            'current_version' => $currentVersion
        ];
    }
    
    /**
     * Download update (required by CustomAppUpdateInterface)
     */
    public function downloadUpdate($updateInfo) {
        $downloadUrl = $updateInfo['download_url'];
        $tempFile = sys_get_temp_dir() . '/update_' . $this->appId . '_' . time() . '.zip';
        
        // In a real implementation, download the update file
        // For demo, we'll create a placeholder
        file_put_contents($tempFile, 'Demo update package for ' . $this->config['app']['name']);
        
        $this->logActivity('app.update_downloaded', 'Update package downloaded', [
            'version' => $updateInfo['new_version'],
            'size' => filesize($tempFile)
        ]);
        
        return [
            'success' => true,
            'file_path' => $tempFile,
            'checksum' => md5_file($tempFile)
        ];
    }
    
    /**
     * Apply update (required by CustomAppUpdateInterface)
     */
    public function applyUpdate($updatePath) {
        $this->logActivity('app.update_started', 'Update process started', [
            'update_path' => $updatePath
        ]);
        
        // In a real implementation, this would:
        // 1. Verify the update package
        // 2. Extract files
        // 3. Update data storage structure if needed
        // 4. Update configuration
        // 5. Clear caches

        $steps = [
            'Verifying update package...',
            'Extracting files...',
            'Updating data storage...',
            'Updating configuration...',
            'Clearing caches...',
            'Finalizing update...'
        ];
        
        foreach ($steps as $step) {
            $this->logActivity('app.update_step', $step);
            usleep(500000); // Simulate processing time
        }
        
        // Update version file
        $newVersion = '1.1.0';
        file_put_contents($this->appPath . '/VERSION', $newVersion);
        
        $this->logActivity('app.update_completed', 'Update completed successfully', [
            'new_version' => $newVersion
        ]);
        
        return [
            'success' => true,
            'message' => 'Update applied successfully',
            'new_version' => $newVersion
        ];
    }
    
    /**
     * Rollback update (required by CustomAppUpdateInterface)
     */
    public function rollbackUpdate($backupPath) {
        $this->logActivity('app.rollback_started', 'Rollback process started', [
            'backup_path' => $backupPath
        ]);
        
        // In a real implementation, this would:
        // 1. Verify backup integrity
        // 2. Stop app services
        // 3. Restore files from backup
        // 4. Restore data files if needed
        // 5. Restart services
        
        $this->logActivity('app.rollback_completed', 'Rollback completed successfully');
        
        return [
            'success' => true,
            'message' => 'Rollback completed successfully'
        ];
    }
    
    /**
     * Log app activity
     */
    public function logActivity($action, $description, $data = []) {
        logActivity($action, $description, array_merge($data, [
            'app_id' => $this->appId,
            'app_name' => $this->config['app']['name']
        ]));
    }
    
    /**
     * Get app configuration
     */
    public function getConfig() {
        return $this->config;
    }
    
    /**
     * Get AI integration instance
     */
    public function getAI() {
        return $this->aiIntegration;
    }
    
    /**
     * Get data storage instance
     */
    public function getDataStorage() {
        return $this->dataStorage;
    }

    /**
     * Get app path
     */
    public function getAppPath() {
        return $this->appPath;
    }
    
    /**
     * Handle app routes
     */
    public function handleRoute($route) {
        $routes = $this->config['routes'] ?? [];
        
        if (isset($routes[$route])) {
            $file = $this->appPath . '/' . $routes[$route];
            if (file_exists($file)) {
                return $file;
            }
        }
        
        // Default to index.php
        return $this->appPath . '/index.php';
    }
    
    /**
     * Get app status for dashboard
     */
    public function getStatus() {
        return [
            'app_id' => $this->appId,
            'name' => $this->config['app']['name'],
            'version' => $this->getVersion(),
            'status' => 'running',
            'last_activity' => date('Y-m-d H:i:s'),
            'features' => $this->config['features'],
            'health' => $this->checkHealth()
        ];
    }
    
    /**
     * Check app health
     */
    private function checkHealth() {
        $health = [
            'status' => 'healthy',
            'checks' => []
        ];
        
        // Check data storage
        try {
            $this->dataStorage->testConnection();
            $health['checks']['data_storage'] = 'ok';
        } catch (Exception $e) {
            $health['checks']['data_storage'] = 'error';
            $health['status'] = 'unhealthy';
        }
        
        // Check AI integration
        try {
            $this->aiIntegration->testConnection();
            $health['checks']['ai'] = 'ok';
        } catch (Exception $e) {
            $health['checks']['ai'] = 'warning';
        }
        
        // Check file permissions
        if (is_writable($this->appPath . '/logs')) {
            $health['checks']['logs'] = 'ok';
        } else {
            $health['checks']['logs'] = 'warning';
        }
        
        return $health;
    }
}

// Initialize the app when this file is included
if (!isset($GLOBALS['sample_app'])) {
    $GLOBALS['sample_app'] = new SampleApp();
}
?>
