<?php
/**
 * Sample App - Settings Interface
 * 
 * This template demonstrates app settings management
 * with user and global settings support.
 */

// Include dashboard configuration and authentication
require_once __DIR__ . '/../../../config.php';
require_once __DIR__ . '/../../../auth.php';
require_once __DIR__ . '/../../../includes/theme.php';

// Require authentication
requireAuth();

// Include app files
require_once __DIR__ . '/../includes/app.php';

// Get the app instance
$app = $GLOBALS['sample_app'];
$config = $app->getConfig();
$dataStorage = $app->getDataStorage();

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    if ($action === 'save_user_settings') {
        $settings = [
            'theme_preference' => $_POST['theme_preference'] ?? 'auto',
            'notifications_enabled' => isset($_POST['notifications_enabled']),
            'auto_save' => isset($_POST['auto_save']),
            'default_data_type' => $_POST['default_data_type'] ?? 'general',
            'items_per_page' => (int)($_POST['items_per_page'] ?? 10)
        ];
        
        foreach ($settings as $key => $value) {
            $dataStorage->saveSetting($key, $value, $_SESSION['user_id'], false);
        }
        
        $app->logActivity('settings.user_updated', 'User settings updated', $settings);
        $successMessage = 'User settings saved successfully!';
    }
    
    if ($action === 'save_global_settings' && isAdmin()) {
        $settings = [
            'app_enabled' => isset($_POST['app_enabled']),
            'max_data_items' => (int)($_POST['max_data_items'] ?? 100),
            'allow_user_themes' => isset($_POST['allow_user_themes']),
            'debug_mode' => isset($_POST['debug_mode'])
        ];
        
        foreach ($settings as $key => $value) {
            $dataStorage->saveSetting($key, $value, null, true);
        }
        
        $app->logActivity('settings.global_updated', 'Global settings updated', $settings);
        $successMessage = 'Global settings saved successfully!';
    }
}

// Load current settings
$userSettings = [
    'theme_preference' => $dataStorage->getSetting('theme_preference', $_SESSION['user_id']) ?? 'auto',
    'notifications_enabled' => $dataStorage->getSetting('notifications_enabled', $_SESSION['user_id']) ?? true,
    'auto_save' => $dataStorage->getSetting('auto_save', $_SESSION['user_id']) ?? true,
    'default_data_type' => $dataStorage->getSetting('default_data_type', $_SESSION['user_id']) ?? 'general',
    'items_per_page' => $dataStorage->getSetting('items_per_page', $_SESSION['user_id']) ?? 10
];

$globalSettings = [
    'app_enabled' => $dataStorage->getSetting('app_enabled') ?? true,
    'max_data_items' => $dataStorage->getSetting('max_data_items') ?? 100,
    'allow_user_themes' => $dataStorage->getSetting('allow_user_themes') ?? true,
    'debug_mode' => $dataStorage->getSetting('debug_mode') ?? false
];

// Set page title
$pageTitle = $config['app']['name'] . ' - Settings';

// Include dashboard header
include __DIR__ . '/../../../templates/header.php';
?>

<div class="space-y-6">
    <!-- Settings Header -->
    <div class="flex justify-between items-center mb-6">
        <div>
            <h1 class="text-3xl font-bold <?php echo getThemeComponentClasses('text-primary'); ?>">
                <?php echo getIcon('settings-gear', 'text-3xl'); ?> App Settings
            </h1>
            <p class="<?php echo getThemeComponentClasses('text-secondary'); ?> mt-1">
                Configure your app preferences and behavior
            </p>
        </div>
        <div class="flex space-x-3">
            <a href="index.php" class="<?php echo getThemeComponentClasses('button-secondary'); ?> px-4 py-2 rounded-lg font-medium transition duration-200">
                Back to App
            </a>
        </div>
    </div>

    <?php if (isset($successMessage)): ?>
        <div class="bg-green-100 dark:bg-green-900 border border-green-400 dark:border-green-600 text-green-700 dark:text-green-200 px-4 py-3 rounded mb-6">
            <?php echo htmlspecialchars($successMessage); ?>
        </div>
    <?php endif; ?>

    <!-- User Settings -->
    <div class="<?php echo getThemeComponentClasses('card'); ?> rounded-lg shadow mb-6">
        <div class="px-6 py-4 <?php echo getThemeComponentClasses('card-header'); ?>">
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white">User Settings</h2>
            <p class="text-sm text-gray-600 dark:text-gray-300 mt-1">These settings apply only to your account</p>
        </div>
        
        <div class="p-6">
            <form method="POST">
                <input type="hidden" name="action" value="save_user_settings">
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Theme Preference -->
                    <div>
                        <label for="theme_preference" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Theme Preference
                        </label>
                        <select name="theme_preference" id="theme_preference" 
                                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg <?php echo getThemeComponentClasses('input'); ?>">
                            <option value="auto" <?php echo $userSettings['theme_preference'] === 'auto' ? 'selected' : ''; ?>>Auto (System)</option>
                            <option value="light" <?php echo $userSettings['theme_preference'] === 'light' ? 'selected' : ''; ?>>Light</option>
                            <option value="dark" <?php echo $userSettings['theme_preference'] === 'dark' ? 'selected' : ''; ?>>Dark</option>
                        </select>
                    </div>
                    
                    <!-- Default Data Type -->
                    <div>
                        <label for="default_data_type" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Default Data Type
                        </label>
                        <select name="default_data_type" id="default_data_type" 
                                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg <?php echo getThemeComponentClasses('input'); ?>">
                            <option value="general" <?php echo $userSettings['default_data_type'] === 'general' ? 'selected' : ''; ?>>General</option>
                            <option value="notes" <?php echo $userSettings['default_data_type'] === 'notes' ? 'selected' : ''; ?>>Notes</option>
                            <option value="settings" <?php echo $userSettings['default_data_type'] === 'settings' ? 'selected' : ''; ?>>Settings</option>
                            <option value="preferences" <?php echo $userSettings['default_data_type'] === 'preferences' ? 'selected' : ''; ?>>Preferences</option>
                        </select>
                    </div>
                    
                    <!-- Items Per Page -->
                    <div>
                        <label for="items_per_page" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Items Per Page
                        </label>
                        <select name="items_per_page" id="items_per_page" 
                                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg <?php echo getThemeComponentClasses('input'); ?>">
                            <option value="5" <?php echo $userSettings['items_per_page'] === 5 ? 'selected' : ''; ?>>5</option>
                            <option value="10" <?php echo $userSettings['items_per_page'] === 10 ? 'selected' : ''; ?>>10</option>
                            <option value="25" <?php echo $userSettings['items_per_page'] === 25 ? 'selected' : ''; ?>>25</option>
                            <option value="50" <?php echo $userSettings['items_per_page'] === 50 ? 'selected' : ''; ?>>50</option>
                        </select>
                    </div>
                </div>
                
                <!-- Checkboxes -->
                <div class="mt-6 space-y-4">
                    <div class="flex items-center">
                        <input type="checkbox" name="notifications_enabled" id="notifications_enabled" 
                               <?php echo $userSettings['notifications_enabled'] ? 'checked' : ''; ?>
                               class="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500">
                        <label for="notifications_enabled" class="ml-2 text-sm text-gray-700 dark:text-gray-300">
                            Enable notifications
                        </label>
                    </div>
                    
                    <div class="flex items-center">
                        <input type="checkbox" name="auto_save" id="auto_save" 
                               <?php echo $userSettings['auto_save'] ? 'checked' : ''; ?>
                               class="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500">
                        <label for="auto_save" class="ml-2 text-sm text-gray-700 dark:text-gray-300">
                            Auto-save changes
                        </label>
                    </div>
                </div>
                
                <div class="mt-6">
                    <button type="submit" class="<?php echo getThemeComponentClasses('button-primary'); ?> px-6 py-2 rounded-lg font-medium transition duration-200">
                        Save User Settings
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Global Settings (Admin Only) -->
    <?php if (isAdmin()): ?>
        <div class="<?php echo getThemeComponentClasses('card'); ?> rounded-lg shadow">
            <div class="px-6 py-4 <?php echo getThemeComponentClasses('card-header'); ?>">
                <h2 class="text-xl font-semibold text-gray-900 dark:text-white">Global Settings</h2>
                <p class="text-sm text-gray-600 dark:text-gray-300 mt-1">These settings apply to all users (Admin only)</p>
            </div>
            
            <div class="p-6">
                <form method="POST">
                    <input type="hidden" name="action" value="save_global_settings">
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Max Data Items -->
                        <div>
                            <label for="max_data_items" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                Max Data Items Per User
                            </label>
                            <input type="number" name="max_data_items" id="max_data_items" 
                                   value="<?php echo htmlspecialchars($globalSettings['max_data_items']); ?>"
                                   min="1" max="1000"
                                   class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg <?php echo getThemeComponentClasses('input'); ?>">
                        </div>
                    </div>
                    
                    <!-- Global Checkboxes -->
                    <div class="mt-6 space-y-4">
                        <div class="flex items-center">
                            <input type="checkbox" name="app_enabled" id="app_enabled" 
                                   <?php echo $globalSettings['app_enabled'] ? 'checked' : ''; ?>
                                   class="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500">
                            <label for="app_enabled" class="ml-2 text-sm text-gray-700 dark:text-gray-300">
                                Enable app for all users
                            </label>
                        </div>
                        
                        <div class="flex items-center">
                            <input type="checkbox" name="allow_user_themes" id="allow_user_themes" 
                                   <?php echo $globalSettings['allow_user_themes'] ? 'checked' : ''; ?>
                                   class="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500">
                            <label for="allow_user_themes" class="ml-2 text-sm text-gray-700 dark:text-gray-300">
                                Allow users to change theme preferences
                            </label>
                        </div>
                        
                        <div class="flex items-center">
                            <input type="checkbox" name="debug_mode" id="debug_mode" 
                                   <?php echo $globalSettings['debug_mode'] ? 'checked' : ''; ?>
                                   class="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500">
                            <label for="debug_mode" class="ml-2 text-sm text-gray-700 dark:text-gray-300">
                                Enable debug mode
                            </label>
                        </div>
                    </div>
                    
                    <div class="mt-6">
                        <button type="submit" class="<?php echo getThemeComponentClasses('button-primary'); ?> px-6 py-2 rounded-lg font-medium transition duration-200">
                            Save Global Settings
                        </button>
                    </div>
                </form>
            </div>
        </div>
    <?php endif; ?>

    <!-- App Information -->
    <div class="<?php echo getThemeComponentClasses('card'); ?> rounded-lg shadow mt-6">
        <div class="px-6 py-4 <?php echo getThemeComponentClasses('card-header'); ?>">
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white">App Information</h2>
        </div>
        
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <h3 class="font-medium text-gray-900 dark:text-white mb-2">App Details</h3>
                    <dl class="space-y-1 text-sm">
                        <div class="flex justify-between">
                            <dt class="text-gray-600 dark:text-gray-300">Name:</dt>
                            <dd class="text-gray-900 dark:text-white"><?php echo htmlspecialchars($config['app']['name']); ?></dd>
                        </div>
                        <div class="flex justify-between">
                            <dt class="text-gray-600 dark:text-gray-300">Version:</dt>
                            <dd class="text-gray-900 dark:text-white"><?php echo htmlspecialchars($app->getVersion()); ?></dd>
                        </div>
                        <div class="flex justify-between">
                            <dt class="text-gray-600 dark:text-gray-300">Storage Type:</dt>
                            <dd class="text-gray-900 dark:text-white">Flat File</dd>
                        </div>
                    </dl>
                </div>
                
                <div>
                    <h3 class="font-medium text-gray-900 dark:text-white mb-2">Features</h3>
                    <ul class="space-y-1 text-sm">
                        <?php foreach ($config['features'] as $feature => $enabled): ?>
                            <li class="flex items-center">
                                <span class="<?php echo $enabled ? 'text-green-600' : 'text-gray-400'; ?>">
                                    <?php echo $enabled ? '✅' : '❌'; ?>
                                </span>
                                <span class="ml-2 text-gray-700 dark:text-gray-300">
                                    <?php echo ucfirst(str_replace('_', ' ', $feature)); ?>
                                </span>
                            </li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include __DIR__ . '/../../../templates/footer.php'; ?>
