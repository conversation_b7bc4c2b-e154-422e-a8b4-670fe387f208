{"app_name": "AI Dashboard", "timezone": "America/New_York", "maintenance_mode": false, "registration_enabled": true, "max_login_attempts": 5, "session_timeout": 3600, "password_min_length": 8, "require_email_verification": false, "default_user_role": "user", "admin_email": "<EMAIL>", "site_description": "Modern AI Dashboard Application", "theme": "default", "enable_logging": true, "log_retention_days": 30, "max_file_upload_size": "10MB", "allowed_file_types": ["jpg", "jpeg", "png", "gif", "pdf", "doc", "docx"], "enable_two_factor": false, "backup_enabled": false, "backup_frequency": "daily", "show_footer_links": false, "footer_links": [{"text": "Privacy Policy", "url": "#"}, {"text": "Terms of Service", "url": "#"}, {"text": "Contact Us", "url": "#"}], "homepage_type": "custom", "custom_homepage_html": "<!DOCTYPE html>\r\n<html lang=\"en\">\r\n<head>\r\n    <meta charset=\"UTF-8\">\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\r\n    <title><PERSON> Developer & AI Aficionado</title>\r\n    \r\n    <!-- Favicon -->\r\n    <link rel=\"icon\" type=\"image/svg+xml\" href=\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath fill='%2300ff88' d='M4 4h24v24H4z'/%3E%3Cpath fill='%23000' d='M8 8h4v4H8zm12 0h4v4h-4zM8 12h16v4H8zm0 8h8v4H8zm12 0h4v4h-4z'/%3E%3C/svg%3E\">\r\n    \r\n    <style>\r\n        @import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Inter:wght@300;400;500;600&display=swap');\r\n\r\n        :root {\r\n            --primary-green: #00ff88;\r\n            --primary-blue: #00d4ff;\r\n            --dark-bg: #0a0a0a;\r\n            --card-bg: #1a1a1a;\r\n            --text-primary: #ffffff;\r\n            --text-secondary: #b0b0b0;\r\n            --accent-purple: #ff00ff;\r\n            --grid-color: rgba(0, 255, 136, 0.1);\r\n        }\r\n\r\n        * {\r\n            margin: 0;\r\n            padding: 0;\r\n            box-sizing: border-box;\r\n        }\r\n\r\n        body {\r\n            font-family: 'Inter', sans-serif;\r\n            background: var(--dark-bg);\r\n            color: var(--text-primary);\r\n            line-height: 1.6;\r\n            overflow-x: hidden;\r\n        }\r\n\r\n        /* Animated grid background */\r\n        .grid-bg {\r\n            position: fixed;\r\n            top: 0;\r\n            left: 0;\r\n            width: 100%;\r\n            height: 100%;\r\n            background-image: \r\n                linear-gradient(rgba(0, 255, 136, 0.1) 1px, transparent 1px),\r\n                linear-gradient(90deg, rgba(0, 255, 136, 0.1) 1px, transparent 1px);\r\n            background-size: 50px 50px;\r\n            animation: grid-move 20s linear infinite;\r\n            z-index: -1;\r\n        }\r\n\r\n        @keyframes grid-move {\r\n            0% { transform: translate(0, 0); }\r\n            100% { transform: translate(50px, 50px); }\r\n        }\r\n\r\n        /* Header */\r\n        header {\r\n            position: fixed;\r\n            top: 0;\r\n            width: 100%;\r\n            padding: 1rem 2rem;\r\n            background: rgba(10, 10, 10, 0.95);\r\n            backdrop-filter: blur(10px);\r\n            border-bottom: 1px solid var(--primary-green);\r\n            z-index: 1000;\r\n        }\r\n\r\n        nav {\r\n            display: flex;\r\n            justify-content: space-between;\r\n            align-items: center;\r\n            max-width: 1200px;\r\n            margin: 0 auto;\r\n        }\r\n\r\n        .logo {\r\n            font-family: 'Orbitron', monospace;\r\n            font-weight: 900;\r\n            font-size: 1.5rem;\r\n            color: var(--primary-green);\r\n            text-shadow: 0 0 10px var(--primary-green);\r\n        }\r\n\r\n        .nav-links {\r\n            display: flex;\r\n            list-style: none;\r\n            gap: 2rem;\r\n        }\r\n\r\n        .nav-links a {\r\n            color: var(--text-secondary);\r\n            text-decoration: none;\r\n            font-weight: 500;\r\n            transition: all 0.3s ease;\r\n            position: relative;\r\n        }\r\n\r\n        .nav-links a:hover {\r\n            color: var(--primary-green);\r\n            text-shadow: 0 0 5px var(--primary-green);\r\n        }\r\n\r\n        .nav-links a::after {\r\n            content: '';\r\n            position: absolute;\r\n            bottom: -5px;\r\n            left: 0;\r\n            width: 0;\r\n            height: 2px;\r\n            background: var(--primary-green);\r\n            transition: width 0.3s ease;\r\n        }\r\n\r\n        .nav-links a:hover::after {\r\n            width: 100%;\r\n        }\r\n\r\n        /* Main content */\r\n        main {\r\n            margin-top: 80px;\r\n        }\r\n\r\n        .container {\r\n            max-width: 1200px;\r\n            margin: 0 auto;\r\n            padding: 0 2rem;\r\n        }\r\n\r\n        /* Hero section */\r\n        .hero {\r\n            min-height: 80vh;\r\n            display: flex;\r\n            align-items: center;\r\n            position: relative;\r\n        }\r\n\r\n        .hero-content {\r\n            flex: 1;\r\n            z-index: 2;\r\n        }\r\n\r\n        .hero h1 {\r\n            font-family: 'Orbitron', monospace;\r\n            font-size: clamp(2.5rem, 5vw, 4rem);\r\n            font-weight: 900;\r\n            margin-bottom: 1rem;\r\n            background: linear-gradient(45deg, var(--primary-green), var(--primary-blue));\r\n            -webkit-background-clip: text;\r\n            -webkit-text-fill-color: transparent;\r\n            background-clip: text;\r\n        }\r\n\r\n        .hero .subtitle {\r\n            font-size: 1.2rem;\r\n            color: var(--text-secondary);\r\n            margin-bottom: 2rem;\r\n            font-weight: 300;\r\n        }\r\n\r\n        .terminal-text {\r\n            font-family: 'Courier New', monospace;\r\n            background: var(--card-bg);\r\n            padding: 1rem;\r\n            border-radius: 8px;\r\n            border: 1px solid var(--primary-green);\r\n            margin-bottom: 2rem;\r\n            position: relative;\r\n        }\r\n\r\n        .terminal-text::before {\r\n            content: '> ';\r\n            color: var(--primary-green);\r\n        }\r\n\r\n        .typing-animation {\r\n            border-right: 2px solid var(--primary-green);\r\n            animation: typing 3s steps(40) infinite;\r\n        }\r\n\r\n        @keyframes typing {\r\n            0%, 50% { border-color: var(--primary-green); }\r\n            51%, 100% { border-color: transparent; }\r\n        }\r\n\r\n        /* Skills section */\r\n        .skills {\r\n            padding: 5rem 0;\r\n            background: linear-gradient(135deg, rgba(26, 26, 26, 0.8), rgba(10, 10, 10, 0.9));\r\n        }\r\n\r\n        .section-title {\r\n            font-family: 'Orbitron', monospace;\r\n            font-size: 2.5rem;\r\n            text-align: center;\r\n            margin-bottom: 3rem;\r\n            color: var(--primary-green);\r\n            text-shadow: 0 0 20px var(--primary-green);\r\n        }\r\n\r\n        .skills-grid {\r\n            display: grid;\r\n            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\r\n            gap: 2rem;\r\n            margin-top: 2rem;\r\n        }\r\n\r\n        .skill-card {\r\n            background: var(--card-bg);\r\n            padding: 2rem;\r\n            border-radius: 12px;\r\n            border: 1px solid transparent;\r\n            transition: all 0.3s ease;\r\n            position: relative;\r\n            overflow: hidden;\r\n        }\r\n\r\n        .skill-card::before {\r\n            content: '';\r\n            position: absolute;\r\n            top: 0;\r\n            left: -100%;\r\n            width: 100%;\r\n            height: 100%;\r\n            background: linear-gradient(90deg, transparent, rgba(0, 255, 136, 0.1), transparent);\r\n            transition: left 0.5s ease;\r\n        }\r\n\r\n        .skill-card:hover::before {\r\n            left: 100%;\r\n        }\r\n\r\n        .skill-card:hover {\r\n            border-color: var(--primary-green);\r\n            transform: translateY(-5px);\r\n            box-shadow: 0 10px 30px rgba(0, 255, 136, 0.2);\r\n        }\r\n\r\n        .skill-icon {\r\n            font-size: 3rem;\r\n            margin-bottom: 1rem;\r\n            display: block;\r\n        }\r\n\r\n        .skill-card:nth-child(1) .skill-icon { color: var(--primary-green); }\r\n        .skill-card:nth-child(2) .skill-icon { color: var(--primary-blue); }\r\n        .skill-card:nth-child(3) .skill-icon { color: var(--accent-purple); }\r\n\r\n        .skill-card h3 {\r\n            font-family: 'Orbitron', monospace;\r\n            font-size: 1.3rem;\r\n            margin-bottom: 1rem;\r\n            color: var(--text-primary);\r\n        }\r\n\r\n        .skill-card p {\r\n            color: var(--text-secondary);\r\n            line-height: 1.6;\r\n        }\r\n\r\n        /* About section */\r\n        .about {\r\n            padding: 5rem 0;\r\n        }\r\n\r\n        .about-content {\r\n            display: grid;\r\n            grid-template-columns: 1fr 1fr;\r\n            gap: 3rem;\r\n            align-items: center;\r\n        }\r\n\r\n        .about-text {\r\n            font-size: 1.1rem;\r\n            line-height: 1.8;\r\n            color: var(--text-secondary);\r\n        }\r\n\r\n        .about-highlight {\r\n            background: var(--card-bg);\r\n            padding: 2rem;\r\n            border-radius: 12px;\r\n            border-left: 4px solid var(--primary-green);\r\n            position: relative;\r\n        }\r\n\r\n        .about-highlight::before {\r\n            content: '//';\r\n            position: absolute;\r\n            top: 1rem;\r\n            right: 1rem;\r\n            color: var(--primary-green);\r\n            font-family: 'Courier New', monospace;\r\n            opacity: 0.5;\r\n        }\r\n\r\n        /* CTA buttons */\r\n        .cta-buttons {\r\n            display: flex;\r\n            gap: 1rem;\r\n            margin-top: 2rem;\r\n            flex-wrap: wrap;\r\n        }\r\n\r\n        .btn {\r\n            padding: 1rem 2rem;\r\n            border: 2px solid var(--primary-green);\r\n            background: transparent;\r\n            color: var(--primary-green);\r\n            font-family: 'Orbitron', monospace;\r\n            font-weight: 600;\r\n            text-decoration: none;\r\n            border-radius: 8px;\r\n            transition: all 0.3s ease;\r\n            position: relative;\r\n            overflow: hidden;\r\n        }\r\n\r\n        .btn::before {\r\n            content: '';\r\n            position: absolute;\r\n            top: 0;\r\n            left: -100%;\r\n            width: 100%;\r\n            height: 100%;\r\n            background: var(--primary-green);\r\n            transition: left 0.3s ease;\r\n            z-index: -1;\r\n        }\r\n\r\n        .btn:hover::before {\r\n            left: 0;\r\n        }\r\n\r\n        .btn:hover {\r\n            color: var(--dark-bg);\r\n            box-shadow: 0 5px 20px rgba(0, 255, 136, 0.3);\r\n        }\r\n\r\n        .btn.secondary {\r\n            border-color: var(--primary-blue);\r\n            color: var(--primary-blue);\r\n        }\r\n\r\n        .btn.secondary::before {\r\n            background: var(--primary-blue);\r\n        }\r\n\r\n        /* Footer */\r\n        footer {\r\n            padding: 3rem 0;\r\n            text-align: center;\r\n            border-top: 1px solid var(--primary-green);\r\n            background: var(--card-bg);\r\n        }\r\n\r\n        .footer-content {\r\n            font-family: 'Courier New', monospace;\r\n            color: var(--text-secondary);\r\n        }\r\n\r\n        /* Responsive design */\r\n        @media (max-width: 768px) {\r\n            .nav-links {\r\n                display: none;\r\n            }\r\n            \r\n            .about-content {\r\n                grid-template-columns: 1fr;\r\n            }\r\n            \r\n            .cta-buttons {\r\n                flex-direction: column;\r\n            }\r\n            \r\n            .skills-grid {\r\n                grid-template-columns: 1fr;\r\n            }\r\n        }\r\n\r\n        /* Scroll indicator */\r\n        .scroll-indicator {\r\n            position: fixed;\r\n            top: 0;\r\n            left: 0;\r\n            width: 0%;\r\n            height: 3px;\r\n            background: linear-gradient(90deg, var(--primary-green), var(--primary-blue));\r\n            z-index: 9999;\r\n            transition: width 0.1s ease;\r\n        }\r\n    </style>\r\n</head>\r\n<body>\r\n    <div class=\"grid-bg\"></div>\r\n    <div class=\"scroll-indicator\" id=\"scrollIndicator\"></div>\r\n    \r\n    <header>\r\n        <nav>\r\n            <div class=\"logo\">EH.dev</div>\r\n            <ul class=\"nav-links\">\r\n                <li><a href=\"#home\">Home</a></li>\r\n                <li><a href=\"#skills\">Skills</a></li>\r\n                <li><a href=\"#about\">About</a></li>\r\n                <li><a href=\"#contact\">Contact</a></li>\r\n            </ul>\r\n        </nav>\r\n    </header>\r\n\r\n    <main>\r\n        <section id=\"home\" class=\"hero\">\r\n            <div class=\"container\">\r\n                <div class=\"hero-content\">\r\n                    <h1>Eric Hamm</h1>\r\n                    <p class=\"subtitle\">UI Developer • WordPress Expert • AI Aficionado</p>\r\n                    \r\n                    <div class=\"terminal-text\">\r\n                        <span class=\"typing-animation\">console.log(\"Building the future, one line of code at a time...\");</span>\r\n                    </div>\r\n                    \r\n                    <div class=\"cta-buttons\">\r\n                        <a href=\"#skills\" class=\"btn\">Explore Skills</a>\r\n                        <a href=\"#about\" class=\"btn secondary\">Learn More</a>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </section>\r\n\r\n        <section id=\"skills\" class=\"skills\">\r\n            <div class=\"container\">\r\n                <h2 class=\"section-title\">Core Expertise</h2>\r\n                <div class=\"skills-grid\">\r\n                    <div class=\"skill-card\">\r\n                        <span class=\"skill-icon\">🎨</span>\r\n                        <h3>UI Developer</h3>\r\n                        <p>Crafting beautiful, responsive user interfaces with modern web technologies. Specializing in creating intuitive user experiences that blend aesthetics with functionality.</p>\r\n                    </div>\r\n                    \r\n                    <div class=\"skill-card\">\r\n                        <span class=\"skill-icon\">⚡</span>\r\n                        <h3>WordPress Developer</h3>\r\n                        <p>Building custom WordPress themes and plugins that extend functionality and deliver powerful content management solutions for businesses of all sizes.</p>\r\n                    </div>\r\n                    \r\n                    <div class=\"skill-card\">\r\n                        <span class=\"skill-icon\">🤖</span>\r\n                        <h3>AI Aficionado</h3>\r\n                        <p>Passionate about artificial intelligence, machine learning, and emerging technologies. Constantly exploring new ways to integrate AI into web development and user experiences.</p>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </section>\r\n\r\n        <section id=\"about\" class=\"about\">\r\n            <div class=\"container\">\r\n                <h2 class=\"section-title\">About Eric</h2>\r\n                <div class=\"about-content\">\r\n                    <div class=\"about-text\">\r\n                        <p>Eric Hamm is an avid AI aficionado who is constantly pursuing greater insights into how AI works, how it's expanding, and how it can be utilized for many powerful and useful purposes.</p>\r\n                        \r\n                        <p>With a passion for both cutting-edge technology and timeless design principles, Eric bridges the gap between innovative AI solutions and practical web development, creating digital experiences that are both intelligent and intuitive.</p>\r\n                    </div>\r\n                    \r\n                    <div class=\"about-highlight\">\r\n                        <h3 style=\"color: var(--primary-green); margin-bottom: 1rem;\">Current Focus</h3>\r\n                        <p>Exploring the intersection of AI and web development, researching how machine learning can enhance user interfaces, and staying at the forefront of emerging technologies that will shape the future of digital experiences.</p>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </section>\r\n    </main>\r\n\r\n    <footer id=\"contact\">\r\n        <div class=\"container\">\r\n            <div class=\"footer-content\">\r\n                <p>// Ready to collaborate? Let's build something amazing together.</p>\r\n                <p style=\"margin-top: 1rem; color: var(--primary-green);\">Eric Hamm © 2025 | Powered by innovation</p>\r\n            </div>\r\n        </div>\r\n    </footer>\r\n\r\n    <script>\r\n        // Smooth scrolling for navigation links\r\n        document.querySelectorAll('a[href^=\"#\"]').forEach(anchor => {\r\n            anchor.addEventListener('click', function (e) {\r\n                e.preventDefault();\r\n                const target = document.querySelector(this.getAttribute('href'));\r\n                if (target) {\r\n                    target.scrollIntoView({\r\n                        behavior: 'smooth',\r\n                        block: 'start'\r\n                    });\r\n                }\r\n            });\r\n        });\r\n\r\n        // Scroll progress indicator\r\n        window.addEventListener('scroll', () => {\r\n            const scrollTop = window.pageYOffset;\r\n            const documentHeight = document.documentElement.scrollHeight - window.innerHeight;\r\n            const scrollPercent = (scrollTop / documentHeight) * 100;\r\n            document.getElementById('scrollIndicator').style.width = scrollPercent + '%';\r\n        });\r\n\r\n        // Typing animation for terminal text\r\n        const terminalText = document.querySelector('.typing-animation');\r\n        const originalText = terminalText.textContent;\r\n        \r\n        function typeWriter() {\r\n            terminalText.textContent = '';\r\n            let i = 0;\r\n            const timer = setInterval(() => {\r\n                if (i < originalText.length) {\r\n                    terminalText.textContent += originalText.charAt(i);\r\n                    i++;\r\n                } else {\r\n                    clearInterval(timer);\r\n                    setTimeout(typeWriter, 3000); // Restart after 3 seconds\r\n                }\r\n            }, 100);\r\n        }\r\n\r\n        // Start typing animation\r\n        typeWriter();\r\n\r\n        // Parallax effect for hero section\r\n        window.addEventListener('scroll', () => {\r\n            const scrolled = window.pageYOffset;\r\n            const hero = document.querySelector('.hero');\r\n            if (hero) {\r\n                hero.style.transform = `translateY(${scrolled * 0.5}px)`;\r\n            }\r\n        });\r\n\r\n        // Add intersection observer for skill cards animation\r\n        const observerOptions = {\r\n            threshold: 0.1,\r\n            rootMargin: '0px 0px -50px 0px'\r\n        };\r\n\r\n        const observer = new IntersectionObserver((entries) => {\r\n            entries.forEach(entry => {\r\n                if (entry.isIntersecting) {\r\n                    entry.target.style.animationDelay = `${Math.random() * 0.5}s`;\r\n                    entry.target.classList.add('animate-in');\r\n                }\r\n            });\r\n        }, observerOptions);\r\n\r\n        // Observe all skill cards\r\n        document.querySelectorAll('.skill-card').forEach(card => {\r\n            observer.observe(card);\r\n        });\r\n\r\n        // Add CSS for animation\r\n        const style = document.createElement('style');\r\n        style.textContent = `\r\n            .skill-card {\r\n                opacity: 0;\r\n                transform: translateY(50px);\r\n                transition: all 0.6s ease;\r\n            }\r\n            \r\n            .skill-card.animate-in {\r\n                opacity: 1;\r\n                transform: translateY(0);\r\n            }\r\n        `;\r\n        document.head.appendChild(style);\r\n\r\n        // Console message for fellow developers\r\n        console.log(`\r\n        ╔═══════════════════════════════════════╗\r\n        ║     🤖 Hello, fellow developer!      ║\r\n        ║                                       ║\r\n        ║   Thanks for checking out the code!   ║\r\n        ║   Built with love by Eric Hamm        ║\r\n        ║                                       ║\r\n        ║   • Modern CSS Grid & Flexbox         ║\r\n        ║   • Vanilla JavaScript               ║\r\n        ║   • Responsive Design                ║\r\n        ║   • AI-inspired aesthetics           ║\r\n        ╚═══════════════════════════════════════╝\r\n        `);\r\n    </script>\r\n</body>\r\n</html>", "dashboard_homepage_app": "ai-story-builder"}